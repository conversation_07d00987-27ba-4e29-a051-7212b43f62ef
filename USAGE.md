# MD2PDF 使用指南

## 快速开始

### 方案1: 简化版转换器（推荐，无需安装依赖）

```bash
# 直接使用，生成HTML文件
node simple-converter.js test/sample.md output/sample.pdf

# 然后在浏览器中打开生成的HTML文件，按Ctrl+P打印为PDF
```

### 方案2: 完整版转换器（需要安装依赖）

```bash
# 1. 安装依赖
node install.js

# 2. 使用命令行工具
npm run cli test/sample.md output/sample.pdf

# 或者直接使用
node bin/cli.js convert test/sample.md output/sample.pdf
```

## 详细使用方法

### 简化版转换器特点

✅ **优点**:
- 无需安装任何依赖包
- 启动速度快
- 支持基本Markdown语法
- 生成高质量HTML

⚠️ **限制**:
- 需要手动在浏览器中转换为PDF
- 表格解析相对简单
- 不支持高级语法高亮

### 完整版转换器特点

✅ **优点**:
- 全自动PDF生成
- 完整的Markdown语法支持
- 代码语法高亮
- 表情符号支持
- 多种预设模板
- 批量转换功能

⚠️ **限制**:
- 需要安装Puppeteer等依赖
- 首次安装较慢
- 占用更多磁盘空间

## 命令行选项

### 完整版命令行选项

```bash
# 基本转换
node bin/cli.js convert input.md output.pdf

# 指定格式和边距
node bin/cli.js convert input.md output.pdf -f A4 -m "2cm"

# 使用预设模板
node bin/cli.js convert input.md output.pdf -t github

# 自定义CSS
node bin/cli.js convert input.md output.pdf --css custom.css

# 批量转换
node bin/cli.js batch ./docs ./output

# 生成HTML预览
node bin/cli.js preview input.md -o preview.html

# 查看文档信息
node bin/cli.js info input.md
```

### 可用选项

- `-f, --format`: PDF格式 (A4, A3, A5, Legal, Letter)
- `-m, --margin`: 页边距 (如 "1cm" 或 "top:2cm,bottom:1cm")
- `-t, --template`: 模板预设 (github, academic, minimal)
- `--css`: 自定义CSS文件路径
- `--no-background`: 禁用背景打印
- `--header`: 页眉HTML模板
- `--footer`: 页脚HTML模板

## 编程接口

### 简单使用

```javascript
const { convertMarkdownToPdf } = require('./src/index');

// 基本转换
await convertMarkdownToPdf('input.md', 'output.pdf');

// 带选项的转换
await convertMarkdownToPdf('input.md', 'output.pdf', {
  format: 'A4',
  margin: { top: '2cm', bottom: '2cm', left: '1.5cm', right: '1.5cm' }
});
```

### 高级使用

```javascript
const { MD2PDFConverter } = require('./src/index');

const converter = new MD2PDFConverter({
  format: 'A4',
  margin: { top: '1cm', bottom: '1cm', left: '1cm', right: '1cm' }
});

// 使用预设模板
converter.usePresetTemplate('github');

// 添加自定义CSS
converter.setCustomCSS(`
  body { font-family: 'Times New Roman', serif; }
  h1 { color: #2c3e50; }
`);

// 转换文件
const result = await converter.convert('input.md', 'output.pdf');
console.log('转换完成:', result);

// 批量转换
const results = await converter.convertBatch('./docs', './output');

// 预览HTML
const preview = await converter.previewHTML('input.md');
console.log('HTML内容:', preview.html);

// 获取文档信息
const info = await converter.getDocumentInfo('input.md');
console.log('文档信息:', info);
```

## 支持的Markdown语法

### 基本语法
- ✅ 标题 (H1-H6)
- ✅ 段落和换行
- ✅ **粗体** 和 *斜体*
- ✅ `行内代码`
- ✅ 代码块（带语法高亮）
- ✅ 链接
- ✅ 图片
- ✅ 列表（有序和无序）
- ✅ 引用块
- ✅ 分割线

### 扩展语法
- ✅ 表格
- ✅ 表情符号 😊 🎉 ⭐
- ✅ 删除线 ~~删除~~
- ✅ 任务列表 - [x] 完成
- ✅ 脚注[^1]

### 图片支持
- ✅ 本地图片 `![alt](./image.png)`
- ✅ 网络图片 `![alt](https://example.com/image.png)`
- ✅ 相对路径自动解析
- ✅ 图片缺失时优雅处理

## 样式模板

### 预设模板

1. **default** - 默认样式，适合一般文档
2. **github** - GitHub风格，适合技术文档
3. **academic** - 学术论文风格，适合正式文档
4. **minimal** - 简约风格，适合简洁文档

### 自定义样式

创建CSS文件并使用 `--css` 选项：

```css
/* custom.css */
body {
  font-family: 'Times New Roman', serif;
  font-size: 12pt;
  line-height: 1.8;
}

h1, h2, h3 {
  color: #2c3e50;
  font-family: Arial, sans-serif;
}

.markdown-table {
  border: 2px solid #34495e;
}
```

## 故障排除

### 常见问题

1. **依赖安装失败**
   ```bash
   # 使用简化版转换器
   node simple-converter.js input.md output.pdf
   ```

2. **Puppeteer下载失败**
   ```bash
   # 设置镜像源
   npm config set puppeteer_download_host=https://npm.taobao.org/mirrors
   npm install puppeteer
   ```

3. **图片不显示**
   - 检查图片路径是否正确
   - 确保图片文件存在
   - 网络图片需要网络连接

4. **表格格式错误**
   - 确保表格语法正确
   - 每行必须有相同数量的列
   - 表头和内容之间需要分隔符行

### 性能优化

1. **大文件处理**
   ```javascript
   // 增加超时时间
   await convertMarkdownToPdf('large.md', 'output.pdf', {
     timeout: 60000  // 60秒
   });
   ```

2. **批量转换**
   ```javascript
   // 使用批量转换而不是循环单个转换
   await converter.convertBatch('./docs', './output');
   ```

## 示例文件

项目包含完整的示例文件 `test/sample.md`，展示了所有支持的功能：

```bash
# 转换示例文件
node simple-converter.js test/sample.md output/sample.pdf

# 或使用完整版
npm run cli test/sample.md output/sample.pdf
```

## 技术架构

```
MD2PDF转换器架构:
Markdown文件 → 解析器 → HTML模板 → Puppeteer → PDF文件
     ↓           ↓         ↓          ↓         ↓
  sample.md → parser.js → template.js → converter.js → output.pdf
```

## 更多帮助

- 查看 `README.md` 了解项目概述
- 查看 `test/sample.md` 了解支持的语法
- 运行 `node test/test.js` 执行完整测试
- 查看源码了解实现细节
