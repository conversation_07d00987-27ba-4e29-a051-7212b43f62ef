const fs = require('fs-extra');
const path = require('path');

class HTMLTemplate {
  constructor(options = {}) {
    this.options = {
      title: 'Markdown Document',
      cssPath: path.join(__dirname, '../styles/default.css'),
      customCSS: '',
      includeHighlightJS: true,
      ...options
    };
  }

  async generateHTML(content, metadata = {}) {
    try {
      // 读取CSS文件
      const cssContent = await this.loadCSS();
      
      // 生成完整的HTML
      const html = this.buildHTMLTemplate(content, cssContent, metadata);
      
      return html;
    } catch (error) {
      throw new Error(`HTML template generation failed: ${error.message}`);
    }
  }

  async loadCSS() {
    let cssContent = '';
    
    // 加载默认CSS
    if (await fs.pathExists(this.options.cssPath)) {
      cssContent += await fs.readFile(this.options.cssPath, 'utf8');
    }
    
    // 加载Highlight.js CSS
    if (this.options.includeHighlightJS) {
      cssContent += this.getHighlightJSCSS();
    }
    
    // 添加自定义CSS
    if (this.options.customCSS) {
      cssContent += '\n' + this.options.customCSS;
    }
    
    return cssContent;
  }

  buildHTMLTemplate(content, cssContent, metadata) {
    const title = metadata.title || this.options.title;
    
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${this.escapeHtml(title)}</title>
    <style>
${cssContent}
    </style>
</head>
<body>
    <div class="markdown-body">
${content}
    </div>
</body>
</html>`;
  }

  getHighlightJSCSS() {
    return `
/* Highlight.js GitHub Theme */
.hljs {
  display: block;
  overflow-x: auto;
  padding: 0.5em;
  color: #333;
  background: #f8f8f8;
}

.hljs-comment,
.hljs-quote {
  color: #998;
  font-style: italic;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-subst {
  color: #333;
  font-weight: bold;
}

.hljs-number,
.hljs-literal,
.hljs-variable,
.hljs-template-variable,
.hljs-tag .hljs-attr {
  color: #008080;
}

.hljs-string,
.hljs-doctag {
  color: #d14;
}

.hljs-title,
.hljs-section,
.hljs-selector-id {
  color: #900;
  font-weight: bold;
}

.hljs-subst {
  font-weight: normal;
}

.hljs-type,
.hljs-class .hljs-title {
  color: #458;
  font-weight: bold;
}

.hljs-tag,
.hljs-name,
.hljs-attribute {
  color: #000080;
  font-weight: normal;
}

.hljs-regexp,
.hljs-link {
  color: #009926;
}

.hljs-symbol,
.hljs-bullet {
  color: #990073;
}

.hljs-built_in,
.hljs-builtin-name {
  color: #0086b3;
}

.hljs-meta {
  color: #999;
  font-weight: bold;
}

.hljs-deletion {
  background: #fdd;
}

.hljs-addition {
  background: #dfd;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}
`;
  }

  escapeHtml(text) {
    const map = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
  }

  // 生成自定义样式的模板
  static createCustomTemplate(options) {
    return new HTMLTemplate(options);
  }

  // 预设模板
  static getPresetTemplates() {
    return {
      github: {
        title: 'GitHub Style Document',
        customCSS: `
          body { 
            font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Helvetica,Arial,sans-serif;
            background-color: #ffffff;
          }
          .markdown-body {
            max-width: 980px;
            margin: 0 auto;
            padding: 45px;
          }
        `
      },
      
      academic: {
        title: 'Academic Paper',
        customCSS: `
          body { 
            font-family: "Times New Roman", Times, serif;
            line-height: 1.8;
            font-size: 12pt;
          }
          h1, h2, h3, h4, h5, h6 {
            font-family: Arial, sans-serif;
          }
          .markdown-body {
            max-width: 210mm;
            margin: 0 auto;
            padding: 2.5cm;
          }
        `
      },
      
      minimal: {
        title: 'Minimal Document',
        customCSS: `
          body { 
            font-family: Georgia, serif;
            color: #2c3e50;
            background-color: #ffffff;
          }
          .markdown-body {
            max-width: 650px;
            margin: 0 auto;
            padding: 2rem;
          }
          h1, h2, h3, h4, h5, h6 {
            color: #34495e;
          }
        `
      }
    };
  }
}

module.exports = HTMLTemplate;
