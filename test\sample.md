# Markdown转PDF测试文档 📄

这是一个测试文档，用于验证Markdown转PDF转换器的各种功能。

## 基本文本格式

这是一个普通段落，包含**粗体文本**、*斜体文本*和`行内代码`。

### 列表测试

#### 无序列表
- 第一项 ✅
- 第二项 🎯
- 第三项包含子列表：
  - 子项目1
  - 子项目2
  - 子项目3

#### 有序列表
1. 首先做这个 🚀
2. 然后做那个 ⭐
3. 最后完成这个 🎉

## 表格测试

| 功能 | 状态 | 描述 | 优先级 |
|------|------|------|--------|
| Markdown解析 | ✅ 完成 | 支持标准语法 | 高 |
| 表格渲染 | ✅ 完成 | 美观的表格样式 | 高 |
| 表情符号 | ✅ 完成 | 支持Unicode表情 | 中 |
| 图片支持 | ✅ 完成 | 本地和网络图片 | 高 |
| 代码高亮 | ✅ 完成 | 多语言语法高亮 | 中 |

## 代码块测试

### JavaScript代码
```javascript
// 示例JavaScript代码
function convertMarkdownToPdf(input, output) {
  const converter = new MD2PDFConverter();
  return converter.convert(input, output, {
    format: 'A4',
    margin: { top: '1cm', bottom: '1cm' }
  });
}

// 使用示例
convertMarkdownToPdf('input.md', 'output.pdf')
  .then(result => console.log('转换成功！', result))
  .catch(error => console.error('转换失败：', error));
```

### Python代码
```python
# 示例Python代码
import os
from pathlib import Path

def process_markdown_files(directory):
    """处理目录中的所有Markdown文件"""
    md_files = Path(directory).glob('*.md')
    
    for file_path in md_files:
        print(f"处理文件: {file_path}")
        # 这里可以调用转换逻辑
        convert_to_pdf(file_path)

# 使用示例
if __name__ == "__main__":
    process_markdown_files("./documents")
```

### CSS样式
```css
/* 自定义PDF样式 */
.markdown-table {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
}

.markdown-table th,
.markdown-table td {
  border: 1px solid #ddd;
  padding: 12px;
  text-align: left;
}

.markdown-table th {
  background-color: #f2f2f2;
  font-weight: bold;
}
```

## 引用块测试

> 这是一个引用块的示例。引用块通常用于突出显示重要信息或引用他人的话。
> 
> 支持多行引用，并且可以包含其他Markdown元素，比如**粗体**和*斜体*。

## 链接测试

- [GitHub项目地址](https://github.com/example/md2pdf)
- [官方文档](https://docs.example.com)
- [在线演示](https://demo.example.com)

## 表情符号测试 😊

这个转换器支持各种表情符号：

- 开心：😊 😄 😃 🙂
- 工作：💻 📝 📊 📈
- 成功：✅ 🎉 🎯 ⭐
- 警告：⚠️ ❌ 🚨 ⛔
- 自然：🌟 🌈 🌸 🍀

## 分割线测试

---

## 图片测试

由于这是示例文件，图片路径可能不存在，但转换器会优雅地处理：

![示例图片](./images/sample.png)

*注意：如果图片不存在，转换器会显示警告但不会中断转换过程。*

## 数学公式（如果支持）

虽然基础版本可能不支持LaTeX数学公式，但可以通过扩展添加：

```
E = mc²
∑(i=1 to n) xi = x1 + x2 + ... + xn
```

## 总结

这个Markdown转PDF转换器具有以下特点：

1. **功能完整** - 支持标准Markdown语法
2. **样式美观** - 提供多种预设模板
3. **扩展性强** - 支持自定义CSS样式
4. **易于使用** - 提供命令行和编程接口
5. **性能优秀** - 基于Puppeteer的高效转换

### 使用建议

- 对于简单文档，使用默认设置即可
- 对于学术论文，推荐使用`academic`模板
- 对于技术文档，推荐使用`github`模板
- 需要自定义样式时，可以提供CSS文件

---

**转换完成时间**: $(date)  
**文档版本**: v1.0  
**转换器版本**: MD2PDF v1.0.0
