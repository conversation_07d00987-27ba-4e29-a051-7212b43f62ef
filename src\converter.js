const puppeteer = require('puppeteer');
const fs = require('fs-extra');
const path = require('path');
const MarkdownParser = require('./parser');
const HTMLTemplate = require('./template');

class PDFConverter {
  constructor(options = {}) {
    this.options = {
      format: 'A4',
      margin: {
        top: '1cm',
        bottom: '1cm',
        left: '1cm',
        right: '1cm'
      },
      printBackground: true,
      displayHeaderFooter: false,
      headerTemplate: '',
      footerTemplate: '',
      timeout: 30000,
      ...options
    };
    
    this.parser = new MarkdownParser();
    this.template = new HTMLTemplate();
  }

  async convertMarkdownToPdf(inputPath, outputPath, customOptions = {}) {
    const options = { ...this.options, ...customOptions };
    let browser = null;
    
    try {
      console.log('🚀 Starting Markdown to PDF conversion...');
      
      // 验证输入文件
      if (!await fs.pathExists(inputPath)) {
        throw new Error(`Input file not found: ${inputPath}`);
      }
      
      // 确保输出目录存在
      await fs.ensureDir(path.dirname(outputPath));
      
      // 读取Markdown文件
      console.log('📖 Reading Markdown file...');
      const markdownContent = await fs.readFile(inputPath, 'utf8');
      
      // 解析Markdown
      console.log('🔄 Parsing Markdown content...');
      const basePath = path.dirname(inputPath);
      const htmlContent = await this.parser.parseMarkdown(markdownContent, basePath);
      const metadata = this.parser.extractMetadata(markdownContent);
      
      // 生成HTML模板
      console.log('🎨 Generating HTML template...');
      const fullHTML = await this.template.generateHTML(htmlContent, metadata);
      
      // 启动浏览器
      console.log('🌐 Launching browser...');
      browser = await puppeteer.launch({
        headless: 'new',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-gpu',
          '--no-first-run',
          '--no-zygote',
          '--single-process'
        ]
      });
      
      const page = await browser.newPage();
      
      // 设置页面内容
      console.log('📄 Setting page content...');
      await page.setContent(fullHTML, {
        waitUntil: ['networkidle0', 'domcontentloaded'],
        timeout: options.timeout
      });
      
      // 等待图片加载
      await this.waitForImages(page);
      
      // 生成PDF
      console.log('📋 Generating PDF...');
      const pdfOptions = {
        path: outputPath,
        format: options.format,
        margin: options.margin,
        printBackground: options.printBackground,
        displayHeaderFooter: options.displayHeaderFooter,
        headerTemplate: options.headerTemplate,
        footerTemplate: options.footerTemplate,
        timeout: options.timeout
      };
      
      await page.pdf(pdfOptions);
      
      console.log('✅ PDF conversion completed successfully!');
      console.log(`📁 Output file: ${outputPath}`);
      
      return {
        success: true,
        inputPath,
        outputPath,
        metadata,
        fileSize: (await fs.stat(outputPath)).size
      };
      
    } catch (error) {
      console.error('❌ PDF conversion failed:', error.message);
      throw error;
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  async waitForImages(page) {
    try {
      // 等待所有图片加载完成
      await page.evaluate(() => {
        return Promise.all(
          Array.from(document.images, img => {
            if (img.complete) return Promise.resolve();
            return new Promise((resolve, reject) => {
              img.addEventListener('load', resolve);
              img.addEventListener('error', reject);
            });
          })
        );
      });
    } catch (error) {
      console.warn('⚠️ Some images may not have loaded properly:', error.message);
    }
  }

  async convertHTMLToPdf(htmlContent, outputPath, customOptions = {}) {
    const options = { ...this.options, ...customOptions };
    let browser = null;
    
    try {
      console.log('🚀 Starting HTML to PDF conversion...');
      
      // 确保输出目录存在
      await fs.ensureDir(path.dirname(outputPath));
      
      // 启动浏览器
      browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
      
      const page = await browser.newPage();
      
      // 设置页面内容
      await page.setContent(htmlContent, {
        waitUntil: ['networkidle0', 'domcontentloaded'],
        timeout: options.timeout
      });
      
      // 等待图片加载
      await this.waitForImages(page);
      
      // 生成PDF
      const pdfOptions = {
        path: outputPath,
        format: options.format,
        margin: options.margin,
        printBackground: options.printBackground,
        displayHeaderFooter: options.displayHeaderFooter,
        headerTemplate: options.headerTemplate,
        footerTemplate: options.footerTemplate
      };
      
      await page.pdf(pdfOptions);
      
      console.log('✅ HTML to PDF conversion completed!');
      
      return {
        success: true,
        outputPath,
        fileSize: (await fs.stat(outputPath)).size
      };
      
    } catch (error) {
      console.error('❌ HTML to PDF conversion failed:', error.message);
      throw error;
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  // 批量转换
  async convertBatch(inputDir, outputDir, customOptions = {}) {
    try {
      console.log('🔄 Starting batch conversion...');
      
      const files = await fs.readdir(inputDir);
      const markdownFiles = files.filter(file => 
        file.endsWith('.md') || file.endsWith('.markdown')
      );
      
      if (markdownFiles.length === 0) {
        throw new Error('No Markdown files found in input directory');
      }
      
      const results = [];
      
      for (const file of markdownFiles) {
        const inputPath = path.join(inputDir, file);
        const outputPath = path.join(outputDir, file.replace(/\.(md|markdown)$/, '.pdf'));
        
        try {
          const result = await this.convertMarkdownToPdf(inputPath, outputPath, customOptions);
          results.push(result);
        } catch (error) {
          console.error(`❌ Failed to convert ${file}:`, error.message);
          results.push({
            success: false,
            inputPath,
            error: error.message
          });
        }
      }
      
      console.log(`✅ Batch conversion completed. ${results.filter(r => r.success).length}/${results.length} files converted successfully.`);
      
      return results;
      
    } catch (error) {
      console.error('❌ Batch conversion failed:', error.message);
      throw error;
    }
  }

  // 获取支持的格式
  static getSupportedFormats() {
    return ['A4', 'A3', 'A5', 'Legal', 'Letter', 'Tabloid'];
  }

  // 预设配置
  static getPresetConfigs() {
    return {
      default: {
        format: 'A4',
        margin: { top: '1cm', bottom: '1cm', left: '1cm', right: '1cm' }
      },
      compact: {
        format: 'A4',
        margin: { top: '0.5cm', bottom: '0.5cm', left: '0.5cm', right: '0.5cm' }
      },
      academic: {
        format: 'A4',
        margin: { top: '2.5cm', bottom: '2.5cm', left: '2.5cm', right: '2.5cm' }
      },
      presentation: {
        format: 'A4',
        margin: { top: '1.5cm', bottom: '1.5cm', left: '1.5cm', right: '1.5cm' },
        displayHeaderFooter: true,
        footerTemplate: '<div style="font-size:10px; text-align:center; width:100%;"><span class="pageNumber"></span> / <span class="totalPages"></span></div>'
      }
    };
  }
}

module.exports = PDFConverter;
