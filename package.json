{"name": "md2pdf-converter", "version": "1.0.0", "description": "A powerful Markdown to PDF converter supporting tables, emojis, and images", "main": "src/index.js", "bin": {"md2pdf": "./bin/cli.js"}, "scripts": {"start": "node src/index.js", "cli": "node bin/cli.js", "test": "node test/test.js", "dev": "node --watch src/index.js"}, "keywords": ["markdown", "pdf", "converter", "puppeteer", "html", "automation"], "author": "Your Name", "license": "MIT", "dependencies": {"puppeteer": "^19.11.1", "marked": "^5.1.1", "highlight.js": "^11.8.0", "node-emoji": "^1.11.0", "commander": "^9.4.1", "chalk": "^4.1.2", "fs-extra": "^10.1.0"}, "devDependencies": {"nodemon": "^3.0.0"}, "engines": {"node": ">=16.0.0"}}