/* MD2PDF Default Styles */

/* 基础样式 */
* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background: white;
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-weight: 600;
  line-height: 1.25;
  color: #1a1a1a;
}

h1 {
  font-size: 2.5rem;
  border-bottom: 3px solid #e1e4e8;
  padding-bottom: 0.5rem;
}

h2 {
  font-size: 2rem;
  border-bottom: 2px solid #e1e4e8;
  padding-bottom: 0.3rem;
}

h3 {
  font-size: 1.5rem;
}

h4 {
  font-size: 1.25rem;
}

h5 {
  font-size: 1.1rem;
}

h6 {
  font-size: 1rem;
  color: #6a737d;
}

/* 段落和文本 */
p {
  margin-bottom: 1rem;
  text-align: justify;
}

/* 链接样式 */
.markdown-link {
  color: #0366d6;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s;
}

.markdown-link:hover {
  border-bottom-color: #0366d6;
}

/* 列表样式 */
ul, ol {
  margin-bottom: 1rem;
  padding-left: 2rem;
}

li {
  margin-bottom: 0.25rem;
}

/* 表格样式 */
.table-container {
  margin: 1.5rem 0;
  overflow-x: auto;
}

.markdown-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  overflow: hidden;
}

.markdown-table th,
.markdown-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e1e4e8;
  border-right: 1px solid #e1e4e8;
}

.markdown-table th {
  background-color: #f6f8fa;
  font-weight: 600;
  color: #24292e;
}

.markdown-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.markdown-table tr:hover {
  background-color: #f5f5f5;
}

.markdown-table th:last-child,
.markdown-table td:last-child {
  border-right: none;
}

.markdown-table tr:last-child td {
  border-bottom: none;
}

/* 代码样式 */
code {
  background-color: #f3f4f6;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.9em;
  color: #e83e8c;
}

.code-block {
  background-color: #f8f9fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 1rem;
  margin: 1rem 0;
  overflow-x: auto;
}

.code-block code {
  background: none;
  padding: 0;
  color: #24292e;
  font-size: 0.85rem;
  line-height: 1.45;
}

/* 语法高亮 */
.hljs-comment,
.hljs-quote {
  color: #6a737d;
  font-style: italic;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-subst {
  color: #d73a49;
  font-weight: bold;
}

.hljs-number,
.hljs-literal,
.hljs-variable,
.hljs-template-variable,
.hljs-tag .hljs-attr {
  color: #005cc5;
}

.hljs-string,
.hljs-doctag {
  color: #032f62;
}

.hljs-title,
.hljs-section,
.hljs-selector-id {
  color: #6f42c1;
  font-weight: bold;
}

/* 图片样式 */
.markdown-image {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  margin: 1rem 0;
  display: block;
}

/* 引用样式 */
blockquote {
  margin: 1rem 0;
  padding: 0 1rem;
  color: #6a737d;
  border-left: 4px solid #dfe2e5;
  background-color: #f8f9fa;
  border-radius: 0 6px 6px 0;
}

blockquote p {
  margin: 0.5rem 0;
}

/* 分割线 */
hr {
  height: 2px;
  background-color: #e1e4e8;
  border: none;
  margin: 2rem 0;
}

/* 表情符号 */
.emoji {
  font-size: 1.2em;
  vertical-align: middle;
}

/* 打印样式 */
@media print {
  body {
    max-width: none;
    margin: 0;
    padding: 1cm;
  }
  
  .code-block {
    page-break-inside: avoid;
  }
  
  .markdown-table {
    page-break-inside: avoid;
  }
  
  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
  }
}
