# MD2PDF Converter

一个强大的Markdown转PDF转换器，支持表格、表情符号、图片和丰富的样式。

## 特性

- ✅ 支持标准Markdown语法
- ✅ 支持表格渲染
- ✅ 支持表情符号 😊 🎉 ⭐
- ✅ 支持图片（本地和网络图片）
- ✅ 支持代码高亮
- ✅ 自定义CSS样式
- ✅ 高质量PDF输出
- ✅ 命令行工具
- ✅ 编程API

## 安装

```bash
npm install
```

## 使用方法

### 命令行使用

```bash
# 转换单个文件
npm run cli -- input.md output.pdf

# 或者使用全局安装后
md2pdf input.md output.pdf
```

### 编程使用

```javascript
const { convertMarkdownToPdf } = require('./src/converter');

await convertMarkdownToPdf('input.md', 'output.pdf', {
  format: 'A4',
  margin: { top: '1cm', bottom: '1cm', left: '1cm', right: '1cm' }
});
```

## 技术栈

- **Puppeteer**: 自动化浏览器，HTML转PDF
- **Marked**: Markdown解析器
- **Highlight.js**: 代码语法高亮
- **Emoji-js**: 表情符号支持

## 目录结构

```
md2pdf-converter/
├── src/
│   ├── index.js          # 主入口
│   ├── converter.js      # PDF转换器
│   ├── parser.js         # Markdown解析器
│   └── template.js       # HTML模板
├── bin/
│   └── cli.js           # 命令行工具
├── test/
│   ├── test.js          # 测试文件
│   └── sample.md        # 示例Markdown
├── styles/
│   └── default.css      # 默认样式
└── output/              # 输出目录
```
