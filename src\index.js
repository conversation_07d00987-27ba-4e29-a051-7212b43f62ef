const PDFConverter = require('./converter');
const MarkdownParser = require('./parser');
const HTMLTemplate = require('./template');
const path = require('path');

class MD2PDFConverter {
  constructor(options = {}) {
    this.converter = new PDFConverter(options);
    this.parser = new MarkdownParser();
    this.template = new HTMLTemplate(options.template || {});
  }

  // 主要转换方法
  async convert(inputPath, outputPath, options = {}) {
    try {
      const result = await this.converter.convertMarkdownToPdf(inputPath, outputPath, options);
      return result;
    } catch (error) {
      throw new Error(`Conversion failed: ${error.message}`);
    }
  }

  // 批量转换
  async convertBatch(inputDir, outputDir, options = {}) {
    try {
      const results = await this.converter.convertBatch(inputDir, outputDir, options);
      return results;
    } catch (error) {
      throw new Error(`Batch conversion failed: ${error.message}`);
    }
  }

  // 从HTML字符串转换
  async convertFromHTML(htmlContent, outputPath, options = {}) {
    try {
      const result = await this.converter.convertHTMLToPdf(htmlContent, outputPath, options);
      return result;
    } catch (error) {
      throw new Error(`HTML conversion failed: ${error.message}`);
    }
  }

  // 预览HTML（不生成PDF）
  async previewHTML(inputPath) {
    try {
      const fs = require('fs-extra');
      const markdownContent = await fs.readFile(inputPath, 'utf8');
      const basePath = path.dirname(inputPath);
      
      const htmlContent = await this.parser.parseMarkdown(markdownContent, basePath);
      const metadata = this.parser.extractMetadata(markdownContent);
      const fullHTML = await this.template.generateHTML(htmlContent, metadata);
      
      return {
        html: fullHTML,
        metadata
      };
    } catch (error) {
      throw new Error(`HTML preview failed: ${error.message}`);
    }
  }

  // 获取文档信息
  async getDocumentInfo(inputPath) {
    try {
      const fs = require('fs-extra');
      const markdownContent = await fs.readFile(inputPath, 'utf8');
      const metadata = this.parser.extractMetadata(markdownContent);
      
      const stats = await fs.stat(inputPath);
      
      return {
        ...metadata,
        filePath: inputPath,
        fileSize: stats.size,
        lastModified: stats.mtime,
        wordCount: markdownContent.split(/\s+/).length,
        characterCount: markdownContent.length
      };
    } catch (error) {
      throw new Error(`Failed to get document info: ${error.message}`);
    }
  }

  // 设置自定义样式
  setCustomCSS(cssContent) {
    this.template.options.customCSS = cssContent;
  }

  // 使用预设模板
  usePresetTemplate(templateName) {
    const presets = HTMLTemplate.getPresetTemplates();
    if (presets[templateName]) {
      this.template = new HTMLTemplate(presets[templateName]);
    } else {
      throw new Error(`Unknown template preset: ${templateName}`);
    }
  }

  // 获取支持的格式
  static getSupportedFormats() {
    return PDFConverter.getSupportedFormats();
  }

  // 获取预设配置
  static getPresetConfigs() {
    return PDFConverter.getPresetConfigs();
  }

  // 获取预设模板
  static getPresetTemplates() {
    return HTMLTemplate.getPresetTemplates();
  }
}

// 导出主要功能
module.exports = {
  MD2PDFConverter,
  PDFConverter,
  MarkdownParser,
  HTMLTemplate,
  
  // 便捷方法
  convertMarkdownToPdf: async (inputPath, outputPath, options = {}) => {
    const converter = new MD2PDFConverter(options);
    return await converter.convert(inputPath, outputPath, options);
  },
  
  convertBatch: async (inputDir, outputDir, options = {}) => {
    const converter = new MD2PDFConverter(options);
    return await converter.convertBatch(inputDir, outputDir, options);
  },
  
  previewHTML: async (inputPath, options = {}) => {
    const converter = new MD2PDFConverter(options);
    return await converter.previewHTML(inputPath);
  }
};

// 如果直接运行此文件
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length < 2) {
    console.log('Usage: node src/index.js <input.md> <output.pdf>');
    console.log('Example: node src/index.js sample.md output.pdf');
    process.exit(1);
  }
  
  const [inputPath, outputPath] = args;
  
  (async () => {
    try {
      const converter = new MD2PDFConverter();
      const result = await converter.convert(inputPath, outputPath);
      
      console.log('✅ Conversion completed successfully!');
      console.log(`📁 Input: ${result.inputPath}`);
      console.log(`📄 Output: ${result.outputPath}`);
      console.log(`📊 File size: ${(result.fileSize / 1024).toFixed(2)} KB`);
      
      if (result.metadata.title) {
        console.log(`📝 Title: ${result.metadata.title}`);
      }
      
    } catch (error) {
      console.error('❌ Conversion failed:', error.message);
      process.exit(1);
    }
  })();
}
