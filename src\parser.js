const { marked } = require('marked');
const hljs = require('highlight.js');
const emoji = require('node-emoji');
const fs = require('fs-extra');
const path = require('path');

class MarkdownParser {
  constructor(options = {}) {
    this.options = {
      breaks: true,
      gfm: true,
      tables: true,
      ...options
    };
    
    this.setupMarked();
  }

  setupMarked() {
    // 配置marked选项
    marked.setOptions({
      breaks: this.options.breaks,
      gfm: this.options.gfm,
      tables: this.options.tables,
      highlight: function(code, lang) {
        if (lang && hljs.getLanguage(lang)) {
          try {
            return hljs.highlight(code, { language: lang }).value;
          } catch (err) {
            console.warn('Highlight.js error:', err);
          }
        }
        return hljs.highlightAuto(code).value;
      }
    });

    // 自定义渲染器
    const renderer = new marked.Renderer();
    
    // 表格渲染增强
    renderer.table = function(header, body) {
      return `<div class="table-container">
        <table class="markdown-table">
          <thead>${header}</thead>
          <tbody>${body}</tbody>
        </table>
      </div>`;
    };

    // 图片渲染增强
    renderer.image = function(href, title, text) {
      let out = '<img src="' + href + '" alt="' + text + '"';
      if (title) {
        out += ' title="' + title + '"';
      }
      out += ' class="markdown-image">';
      return out;
    };

    // 代码块渲染增强
    renderer.code = function(code, infostring, escaped) {
      const lang = (infostring || '').match(/\S*/)[0];
      if (this.options.highlight) {
        const out = this.options.highlight(code, lang);
        if (out != null && out !== code) {
          escaped = true;
          code = out;
        }
      }

      if (!lang) {
        return '<pre class="code-block"><code>' +
          (escaped ? code : this.escape(code, true)) +
          '</code></pre>\n';
      }

      return '<pre class="code-block"><code class="language-' +
        this.escape(lang, true) +
        '">' +
        (escaped ? code : this.escape(code, true)) +
        '</code></pre>\n';
    };

    // 链接渲染增强
    renderer.link = function(href, title, text) {
      let out = '<a href="' + href + '"';
      if (title) {
        out += ' title="' + title + '"';
      }
      out += ' class="markdown-link">' + text + '</a>';
      return out;
    };

    marked.use({ renderer });
  }

  async parseMarkdown(markdownContent, basePath = '') {
    try {
      // 处理表情符号
      const emojiProcessed = emoji.emojify(markdownContent);
      
      // 处理相对路径图片
      const imageProcessed = await this.processImages(emojiProcessed, basePath);
      
      // 转换为HTML
      const html = marked(imageProcessed);
      
      return html;
    } catch (error) {
      throw new Error(`Markdown parsing failed: ${error.message}`);
    }
  }

  async processImages(content, basePath) {
    // 匹配Markdown图片语法
    const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g;
    let processedContent = content;
    
    const matches = [...content.matchAll(imageRegex)];
    
    for (const match of matches) {
      const [fullMatch, altText, imagePath] = match;
      
      // 如果是相对路径，转换为绝对路径
      if (!imagePath.startsWith('http') && !path.isAbsolute(imagePath)) {
        const absolutePath = path.resolve(basePath, imagePath);
        
        // 检查文件是否存在
        if (await fs.pathExists(absolutePath)) {
          const replacement = `![${altText}](file://${absolutePath.replace(/\\/g, '/')})`;
          processedContent = processedContent.replace(fullMatch, replacement);
        } else {
          console.warn(`Image not found: ${absolutePath}`);
        }
      }
    }
    
    return processedContent;
  }

  // 提取文档元数据
  extractMetadata(content) {
    const metadata = {
      title: '',
      headings: [],
      images: [],
      tables: 0,
      codeBlocks: 0
    };

    // 提取标题
    const titleMatch = content.match(/^#\s+(.+)$/m);
    if (titleMatch) {
      metadata.title = titleMatch[1];
    }

    // 提取所有标题
    const headingMatches = content.match(/^#{1,6}\s+.+$/gm);
    if (headingMatches) {
      metadata.headings = headingMatches.map(h => h.trim());
    }

    // 统计图片
    const imageMatches = content.match(/!\[([^\]]*)\]\(([^)]+)\)/g);
    if (imageMatches) {
      metadata.images = imageMatches;
    }

    // 统计表格
    const tableMatches = content.match(/\|.*\|/g);
    if (tableMatches) {
      metadata.tables = Math.floor(tableMatches.length / 2); // 估算表格数量
    }

    // 统计代码块
    const codeBlockMatches = content.match(/```[\s\S]*?```/g);
    if (codeBlockMatches) {
      metadata.codeBlocks = codeBlockMatches.length;
    }

    return metadata;
  }
}

module.exports = MarkdownParser;
