const { MD2PDFConverter, convertMarkdownToPdf } = require('../src/index');
const path = require('path');
const fs = require('fs-extra');

async function runTests() {
  console.log('🧪 Starting MD2PDF Tests...\n');
  
  const testResults = [];
  
  // 测试1: 基本转换功能
  try {
    console.log('📝 Test 1: Basic Markdown to PDF conversion');
    
    const inputPath = path.join(__dirname, 'sample.md');
    const outputPath = path.join(__dirname, '../output/test-basic.pdf');
    
    const result = await convertMarkdownToPdf(inputPath, outputPath);
    
    if (result.success && await fs.pathExists(outputPath)) {
      console.log('✅ Test 1 PASSED');
      console.log(`   Output: ${result.outputPath}`);
      console.log(`   Size: ${(result.fileSize / 1024).toFixed(2)} KB`);
      testResults.push({ test: 'Basic conversion', status: 'PASSED' });
    } else {
      throw new Error('PDF file not created');
    }
    
  } catch (error) {
    console.log('❌ Test 1 FAILED:', error.message);
    testResults.push({ test: 'Basic conversion', status: 'FAILED', error: error.message });
  }
  
  console.log('');
  
  // 测试2: 自定义选项
  try {
    console.log('📝 Test 2: Custom options conversion');
    
    const inputPath = path.join(__dirname, 'sample.md');
    const outputPath = path.join(__dirname, '../output/test-custom.pdf');
    
    const options = {
      format: 'A4',
      margin: { top: '2cm', bottom: '2cm', left: '1.5cm', right: '1.5cm' },
      printBackground: true
    };
    
    const result = await convertMarkdownToPdf(inputPath, outputPath, options);
    
    if (result.success && await fs.pathExists(outputPath)) {
      console.log('✅ Test 2 PASSED');
      console.log(`   Output: ${result.outputPath}`);
      testResults.push({ test: 'Custom options', status: 'PASSED' });
    } else {
      throw new Error('PDF file not created');
    }
    
  } catch (error) {
    console.log('❌ Test 2 FAILED:', error.message);
    testResults.push({ test: 'Custom options', status: 'FAILED', error: error.message });
  }
  
  console.log('');
  
  // 测试3: HTML预览
  try {
    console.log('📝 Test 3: HTML preview generation');
    
    const converter = new MD2PDFConverter();
    const inputPath = path.join(__dirname, 'sample.md');
    const result = await converter.previewHTML(inputPath);
    
    if (result.html && result.html.includes('<html>')) {
      console.log('✅ Test 3 PASSED');
      console.log(`   HTML length: ${result.html.length} characters`);
      console.log(`   Title: ${result.metadata.title || 'No title'}`);
      
      // 保存HTML预览
      const htmlPath = path.join(__dirname, '../output/test-preview.html');
      await fs.writeFile(htmlPath, result.html, 'utf8');
      console.log(`   HTML saved: ${htmlPath}`);
      
      testResults.push({ test: 'HTML preview', status: 'PASSED' });
    } else {
      throw new Error('Invalid HTML generated');
    }
    
  } catch (error) {
    console.log('❌ Test 3 FAILED:', error.message);
    testResults.push({ test: 'HTML preview', status: 'FAILED', error: error.message });
  }
  
  console.log('');
  
  // 测试4: 文档信息提取
  try {
    console.log('📝 Test 4: Document information extraction');
    
    const converter = new MD2PDFConverter();
    const inputPath = path.join(__dirname, 'sample.md');
    const info = await converter.getDocumentInfo(inputPath);
    
    if (info.wordCount > 0 && info.characterCount > 0) {
      console.log('✅ Test 4 PASSED');
      console.log(`   Words: ${info.wordCount}`);
      console.log(`   Characters: ${info.characterCount}`);
      console.log(`   Headings: ${info.headings.length}`);
      console.log(`   Images: ${info.images.length}`);
      console.log(`   Tables: ${info.tables}`);
      console.log(`   Code blocks: ${info.codeBlocks}`);
      
      testResults.push({ test: 'Document info', status: 'PASSED' });
    } else {
      throw new Error('Invalid document information');
    }
    
  } catch (error) {
    console.log('❌ Test 4 FAILED:', error.message);
    testResults.push({ test: 'Document info', status: 'FAILED', error: error.message });
  }
  
  console.log('');
  
  // 测试5: 预设模板
  try {
    console.log('📝 Test 5: Preset templates');
    
    const converter = new MD2PDFConverter();
    const inputPath = path.join(__dirname, 'sample.md');
    
    // 测试GitHub模板
    converter.usePresetTemplate('github');
    const outputPath = path.join(__dirname, '../output/test-github-template.pdf');
    const result = await converter.convert(inputPath, outputPath);
    
    if (result.success && await fs.pathExists(outputPath)) {
      console.log('✅ Test 5 PASSED');
      console.log(`   GitHub template output: ${result.outputPath}`);
      testResults.push({ test: 'Preset templates', status: 'PASSED' });
    } else {
      throw new Error('Template conversion failed');
    }
    
  } catch (error) {
    console.log('❌ Test 5 FAILED:', error.message);
    testResults.push({ test: 'Preset templates', status: 'FAILED', error: error.message });
  }
  
  console.log('');
  
  // 测试结果汇总
  console.log('📊 Test Results Summary:');
  console.log('========================');
  
  const passed = testResults.filter(r => r.status === 'PASSED').length;
  const failed = testResults.filter(r => r.status === 'FAILED').length;
  
  testResults.forEach(result => {
    const status = result.status === 'PASSED' ? '✅' : '❌';
    console.log(`${status} ${result.test}: ${result.status}`);
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
  });
  
  console.log('');
  console.log(`Total: ${testResults.length} tests`);
  console.log(`Passed: ${passed}`);
  console.log(`Failed: ${failed}`);
  
  if (failed === 0) {
    console.log('🎉 All tests passed!');
  } else {
    console.log('⚠️ Some tests failed. Please check the errors above.');
  }
  
  return { passed, failed, total: testResults.length };
}

// 运行测试
if (require.main === module) {
  runTests()
    .then(results => {
      process.exit(results.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('❌ Test runner failed:', error);
      process.exit(1);
    });
}

module.exports = { runTests };
