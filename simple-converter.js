/**
 * 简化版Markdown转PDF转换器
 * 不依赖外部包，使用Node.js内置模块
 */

const fs = require('fs');
const path = require('path');

class SimpleMarkdownToPDF {
  constructor() {
    this.cssStyles = this.getDefaultCSS();
  }

  // 简单的Markdown解析器
  parseMarkdown(content) {
    let html = content;
    
    // 标题
    html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
    html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
    html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');
    
    // 粗体和斜体
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
    
    // 行内代码
    html = html.replace(/`(.*?)`/g, '<code>$1</code>');
    
    // 代码块
    html = html.replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code>$2</code></pre>');
    
    // 链接
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');
    
    // 图片
    html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" style="max-width:100%;height:auto;">');
    
    // 无序列表
    html = html.replace(/^\s*\* (.+)$/gm, '<li>$1</li>');
    html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');
    
    // 有序列表
    html = html.replace(/^\s*\d+\. (.+)$/gm, '<li>$1</li>');
    
    // 引用
    html = html.replace(/^> (.+)$/gm, '<blockquote>$1</blockquote>');
    
    // 分割线
    html = html.replace(/^---$/gm, '<hr>');
    
    // 段落
    html = html.replace(/\n\n/g, '</p><p>');
    html = '<p>' + html + '</p>';
    
    // 清理空段落
    html = html.replace(/<p><\/p>/g, '');
    html = html.replace(/<p>(<h[1-6]>)/g, '$1');
    html = html.replace(/(<\/h[1-6]>)<\/p>/g, '$1');
    html = html.replace(/<p>(<ul>)/g, '$1');
    html = html.replace(/(<\/ul>)<\/p>/g, '$1');
    html = html.replace(/<p>(<blockquote>)/g, '$1');
    html = html.replace(/(<\/blockquote>)<\/p>/g, '$1');
    html = html.replace(/<p>(<hr>)<\/p>/g, '$1');
    
    return html;
  }

  // 简单的表格解析
  parseTable(content) {
    const lines = content.split('\n');
    let html = content;
    let inTable = false;
    let tableRows = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      if (line.includes('|') && !line.startsWith('|---')) {
        if (!inTable) {
          inTable = true;
          tableRows = [];
        }
        
        const cells = line.split('|').map(cell => cell.trim()).filter(cell => cell);
        tableRows.push(cells);
      } else if (inTable && line.startsWith('|---')) {
        // 表格分隔符，跳过
        continue;
      } else if (inTable) {
        // 表格结束
        if (tableRows.length > 0) {
          const tableHTML = this.generateTableHTML(tableRows);
          // 替换原始表格内容
          const tableStart = lines.indexOf(lines.find(l => l.includes('|') && tableRows[0].join('|').includes(l.split('|')[1])));
          const tableEnd = i - 1;
          
          // 这里简化处理，直接在最后添加表格
          html += '\n' + tableHTML;
        }
        inTable = false;
        tableRows = [];
      }
    }
    
    return html;
  }

  generateTableHTML(rows) {
    if (rows.length === 0) return '';
    
    let html = '<table class="markdown-table">';
    
    // 表头
    html += '<thead><tr>';
    rows[0].forEach(cell => {
      html += `<th>${cell}</th>`;
    });
    html += '</tr></thead>';
    
    // 表体
    if (rows.length > 1) {
      html += '<tbody>';
      for (let i = 1; i < rows.length; i++) {
        html += '<tr>';
        rows[i].forEach(cell => {
          html += `<td>${cell}</td>`;
        });
        html += '</tr>';
      }
      html += '</tbody>';
    }
    
    html += '</table>';
    return html;
  }

  // 生成完整HTML
  generateHTML(markdownContent, title = 'Markdown Document') {
    // 先处理表格
    const contentWithTables = this.parseTable(markdownContent);
    
    // 再处理其他Markdown语法
    const htmlContent = this.parseMarkdown(contentWithTables);
    
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>${this.cssStyles}</style>
</head>
<body>
    <div class="markdown-body">
        ${htmlContent}
    </div>
</body>
</html>`;
  }

  // 默认CSS样式
  getDefaultCSS() {
    return `
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        background: white;
      }
      
      h1, h2, h3, h4, h5, h6 {
        margin-top: 2rem;
        margin-bottom: 1rem;
        font-weight: 600;
        line-height: 1.25;
        color: #1a1a1a;
      }
      
      h1 { font-size: 2.5rem; border-bottom: 3px solid #e1e4e8; padding-bottom: 0.5rem; }
      h2 { font-size: 2rem; border-bottom: 2px solid #e1e4e8; padding-bottom: 0.3rem; }
      h3 { font-size: 1.5rem; }
      
      p { margin-bottom: 1rem; text-align: justify; }
      
      code {
        background-color: #f3f4f6;
        padding: 0.2rem 0.4rem;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
        font-size: 0.9em;
        color: #e83e8c;
      }
      
      pre {
        background-color: #f8f9fa;
        border: 1px solid #e1e4e8;
        border-radius: 6px;
        padding: 1rem;
        margin: 1rem 0;
        overflow-x: auto;
      }
      
      pre code {
        background: none;
        padding: 0;
        color: #24292e;
        font-size: 0.85rem;
      }
      
      .markdown-table {
        width: 100%;
        border-collapse: collapse;
        border-spacing: 0;
        border: 1px solid #e1e4e8;
        border-radius: 6px;
        overflow: hidden;
        margin: 1.5rem 0;
      }
      
      .markdown-table th,
      .markdown-table td {
        padding: 12px 16px;
        text-align: left;
        border-bottom: 1px solid #e1e4e8;
        border-right: 1px solid #e1e4e8;
      }
      
      .markdown-table th {
        background-color: #f6f8fa;
        font-weight: 600;
        color: #24292e;
      }
      
      .markdown-table tr:nth-child(even) {
        background-color: #f9f9f9;
      }
      
      ul, ol { margin-bottom: 1rem; padding-left: 2rem; }
      li { margin-bottom: 0.25rem; }
      
      blockquote {
        margin: 1rem 0;
        padding: 0 1rem;
        color: #6a737d;
        border-left: 4px solid #dfe2e5;
        background-color: #f8f9fa;
        border-radius: 0 6px 6px 0;
      }
      
      a { color: #0366d6; text-decoration: none; }
      a:hover { text-decoration: underline; }
      
      hr {
        height: 2px;
        background-color: #e1e4e8;
        border: none;
        margin: 2rem 0;
      }
      
      img {
        max-width: 100%;
        height: auto;
        border-radius: 6px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        margin: 1rem 0;
        display: block;
      }
    `;
  }

  // 转换文件
  async convertFile(inputPath, outputPath) {
    try {
      console.log('📖 Reading Markdown file...');
      const markdownContent = fs.readFileSync(inputPath, 'utf8');
      
      console.log('🔄 Converting to HTML...');
      const html = this.generateHTML(markdownContent);
      
      // 保存HTML文件（用于预览）
      const htmlPath = outputPath.replace('.pdf', '.html');
      fs.writeFileSync(htmlPath, html, 'utf8');
      
      console.log('✅ HTML file generated:', htmlPath);
      console.log('');
      console.log('📋 Next steps to generate PDF:');
      console.log('1. Open the HTML file in Chrome/Edge browser');
      console.log('2. Press Ctrl+P (or Cmd+P on Mac)');
      console.log('3. Choose "Save as PDF" as destination');
      console.log('4. Adjust settings as needed and save');
      console.log('');
      console.log('🔧 For automated PDF generation, install dependencies:');
      console.log('   npm install puppeteer marked highlight.js');
      console.log('   Then use the full converter: npm run cli input.md output.pdf');
      
      return {
        success: true,
        htmlPath,
        message: 'HTML generated successfully. Use browser to convert to PDF.'
      };
      
    } catch (error) {
      console.error('❌ Conversion failed:', error.message);
      throw error;
    }
  }
}

// 命令行使用
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length < 2) {
    console.log('Usage: node simple-converter.js <input.md> <output.pdf>');
    console.log('Example: node simple-converter.js test/sample.md output/sample.pdf');
    process.exit(1);
  }
  
  const [inputPath, outputPath] = args;
  const converter = new SimpleMarkdownToPDF();
  
  converter.convertFile(inputPath, outputPath)
    .then(result => {
      console.log('🎉 Conversion completed!');
    })
    .catch(error => {
      console.error('💥 Error:', error.message);
      process.exit(1);
    });
}

module.exports = SimpleMarkdownToPDF;
