<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown转PDF测试文档 📄</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        background: white;
      }
      
      h1, h2, h3, h4, h5, h6 {
        margin-top: 2rem;
        margin-bottom: 1rem;
        font-weight: 600;
        line-height: 1.25;
        color: #1a1a1a;
      }
      
      h1 { font-size: 2.5rem; border-bottom: 3px solid #e1e4e8; padding-bottom: 0.5rem; }
      h2 { font-size: 2rem; border-bottom: 2px solid #e1e4e8; padding-bottom: 0.3rem; }
      h3 { font-size: 1.5rem; }
      h4 { font-size: 1.25rem; }
      h5 { font-size: 1.1rem; }
      h6 { font-size: 1rem; color: #6a737d; }
      
      p { margin-bottom: 1rem; text-align: justify; }
      
      code {
        background-color: #f3f4f6;
        padding: 0.2rem 0.4rem;
        border-radius: 3px;
        font-family: 'Courier New', 'Consolas', monospace;
        font-size: 0.9em;
        color: #e83e8c;
      }
      
      .code-block {
        background-color: #f8f9fa;
        border: 1px solid #e1e4e8;
        border-radius: 6px;
        padding: 1rem;
        margin: 1rem 0;
        overflow-x: auto;
        position: relative;
      }
      
      .code-block::before {
        content: attr(class);
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        font-size: 0.7rem;
        color: #6a737d;
        text-transform: uppercase;
      }
      
      .code-block code {
        background: none;
        padding: 0;
        color: #24292e;
        font-size: 0.85rem;
        line-height: 1.45;
      }
      
      .table-container {
        margin: 1.5rem 0;
        overflow-x: auto;
      }
      
      .markdown-table {
        width: 100%;
        border-collapse: collapse;
        border-spacing: 0;
        border: 1px solid #e1e4e8;
        border-radius: 6px;
        overflow: hidden;
      }
      
      .markdown-table th,
      .markdown-table td {
        padding: 12px 16px;
        text-align: left;
        border-bottom: 1px solid #e1e4e8;
        border-right: 1px solid #e1e4e8;
      }
      
      .markdown-table th {
        background-color: #f6f8fa;
        font-weight: 600;
        color: #24292e;
      }
      
      .markdown-table tr:nth-child(even) {
        background-color: #f9f9f9;
      }
      
      .markdown-table tr:hover {
        background-color: #f5f5f5;
      }
      
      ul, ol { margin-bottom: 1rem; padding-left: 2rem; }
      li { margin-bottom: 0.25rem; }
      
      .task-item {
        list-style: none;
        margin-left: -1.5rem;
      }
      
      .task-item input[type="checkbox"] {
        margin-right: 0.5rem;
      }
      
      blockquote {
        margin: 1rem 0;
        padding: 0 1rem;
        color: #6a737d;
        border-left: 4px solid #dfe2e5;
        background-color: #f8f9fa;
        border-radius: 0 6px 6px 0;
      }
      
      a { color: #0366d6; text-decoration: none; }
      a:hover { text-decoration: underline; }
      
      hr {
        height: 2px;
        background-color: #e1e4e8;
        border: none;
        margin: 2rem 0;
      }
      
      .markdown-image {
        max-width: 100%;
        height: auto;
        border-radius: 6px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        margin: 1rem 0;
        display: block;
      }
      
      del {
        color: #6a737d;
        text-decoration: line-through;
      }
      
      @media print {
        body { max-width: none; margin: 0; padding: 1cm; }
        .code-block { page-break-inside: avoid; }
        .markdown-table { page-break-inside: avoid; }
        h1, h2, h3, h4, h5, h6 { page-break-after: avoid; }
      }
    
      body {
        font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Helvetica,Arial,sans-serif;
        background-color: #ffffff;
      }
      
      h1, h2 {
        border-bottom: 1px solid #eaecef;
      }
      
      .code-block {
        background-color: #f6f8fa;
        border: 1px solid #d0d7de;
      }
    </style>
</head>
<body>
    <div class="markdown-body">
        <h1 id="markdownpdf-">Markdown转PDF测试文档 📄</h1>
<p>这是一个测试文档，用于验证Markdown转PDF转换器的各种功能。</p>
<h2 id="">基本文本格式</h2>
<p>这是一个普通段落，包含<strong>粗体文本</strong>、<em>斜体文本</em>和<code>行内代码</code>。</p>
<h3 id="">列表测试</h3>
<h4 id="">无序列表</h4>
<ul><li>第一项 ✅</li>
<li>第二项 🎯</li>
<li>第三项包含子列表：</li>
<li>子项目1</li>
<li>子项目2</li>
<li>子项目3</li>
<h4 id="">有序列表</h4>
<li>首先做这个 🚀</li>
<li>然后做那个 ⭐</li>
<li>最后完成这个 🎉</li>
<h2 id="">表格测试</h2>
<div class="table-container"><table class="markdown-table"><thead><tr><th>功能</th><th>状态</th><th>描述</th><th>优先级</th></tr></thead><tbody><tr><td>Markdown解析</td><td>✅ 完成</td><td>支持标准语法</td><td>高</td></tr><tr><td>表格渲染</td><td>✅ 完成</td><td>美观的表格样式</td><td>高</td></tr><tr><td>表情符号</td><td>✅ 完成</td><td>支持Unicode表情</td><td>中</td></tr><tr><td>图片支持</td><td>✅ 完成</td><td>本地和网络图片</td><td>高</td></tr><tr><td>代码高亮</td><td>✅ 完成</td><td>多语言语法高亮</td><td>中</td></tr></tbody></table></div>
<h2 id="">代码块测试</h2>
<h3 id="javascript">JavaScript代码</h3>
<pre class="code-block language-javascript"><code class="language-javascript">// 示例JavaScript代码
function convertMarkdownToPdf(input, output) {
  const converter = new MD2PDFConverter();
  return converter.convert(input, output, {
    format: &#039;A4&#039;,
    margin: { top: &#039;1cm&#039;, bottom: &#039;1cm&#039; }
  });
}
<p>// 使用示例
convertMarkdownToPdf(&#039;input.md&#039;, &#039;output.pdf&#039;)
  .then(result =&gt; console.log(&#039;转换成功！&#039;, result))
  .catch(error =&gt; console.error(&#039;转换失败：&#039;, error));</code></pre>
<h3 id="python">Python代码</h3>
<pre class="code-block language-python"><code class="language-python"># 示例Python代码
import os
from pathlib import Path
<p>def process_markdown_files(directory):
    &quot;&quot;&quot;处理目录中的所有Markdown文件&quot;&quot;&quot;
    md_files = Path(directory).glob(&#039;*.md&#039;)
    
    for file_path in md_files:
        print(f&quot;处理文件: {file_path}&quot;)
        # 这里可以调用转换逻辑
        convert_to_pdf(file_path)</p>
<h1 id="">使用示例</h1>
if __name__ == &quot;__main__&quot;:
    process_markdown_files(&quot;./documents&quot;)</code></pre>
<h3 id="css">CSS样式</h3>
<pre class="code-block language-css"><code class="language-css">/<em> 自定义PDF样式 </em>/
.markdown-table {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
}
<p>.markdown-table th,
.markdown-table td {
  border: 1px solid #ddd;
  padding: 12px;
  text-align: left;
}</p>
<p>.markdown-table th {
  background-color: #f2f2f2;
  font-weight: bold;
}</code></pre>
<h2 id="">引用块测试</h2>
<blockquote>这是一个引用块的示例。引用块通常用于突出显示重要信息或引用他人的话。</blockquote>
<blockquote>> 支持多行引用，并且可以包含其他Markdown元素，比如<strong>粗体</strong>和<em>斜体</em>。</blockquote>
<h2 id="">链接测试</h2>
<li><a href="https://github.com/example/md2pdf" target="_blank">GitHub项目地址</a></li>
<li><a href="https://docs.example.com" target="_blank">官方文档</a></li>
<li><a href="https://demo.example.com" target="_blank">在线演示</a></li>
<h2 id="-">表情符号测试 😊</h2>
<p>这个转换器支持各种表情符号：
<li>开心：😊 😄 😃 🙂</li>
<li>工作：💻 📝 📊 📈</li>
<li>成功：✅ 🎉 🎯 ⭐</li>
<li>警告：⚠️ ❌ 🚨 ⛔</li>
<li>自然：🌟 🌈 🌸 🍀</li></p>
<h2 id="">分割线测试</h2>
<hr>
<h2 id="">图片测试</h2>
<p>由于这是示例文件，图片路径可能不存在，但转换器会优雅地处理：</p>
<p>!<a href="./images/sample.png" target="_blank">示例图片</a></p>
<p><em>注意：如果图片不存在，转换器会显示警告但不会中断转换过程。</em></p>
<h2 id="">数学公式（如果支持）</h2>
<p>虽然基础版本可能不支持LaTeX数学公式，但可以通过扩展添加：</p>
<pre class="code-block language-text"><code class="language-text">E = mc²
∑(i=1 to n) xi = x1 + x2 + ... + xn</code></pre>
<h2 id="">总结</h2>
<p>这个Markdown转PDF转换器具有以下特点：
<li><strong>功能完整</strong> - 支持标准Markdown语法</li>
<li><strong>样式美观</strong> - 提供多种预设模板</li>
<li><strong>扩展性强</strong> - 支持自定义CSS样式</li>
<li><strong>易于使用</strong> - 提供命令行和编程接口</li>
<li><strong>性能优秀</strong> - 基于Puppeteer的高效转换</li></p>
<h3 id="">使用建议</h3>
<li>对于简单文档，使用默认设置即可</li>
<li>对于学术论文，推荐使用<code>academic</code>模板</li>
<li>对于技术文档，推荐使用<code>github</code>模板</li>
<li>需要自定义样式时，可以提供CSS文件</li></ul>
<hr>
<p><strong>转换完成时间</strong>: $(date)  
<strong>文档版本</strong>: v1.0  
<strong>转换器版本</strong>: MD2PDF v1.0.0</p>
    </div>
</body>
</html>