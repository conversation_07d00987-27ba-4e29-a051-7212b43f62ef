/**
 * 安装脚本 - 处理依赖安装和环境检查
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class Installer {
  constructor() {
    this.dependencies = [
      'puppeteer@19.11.1',
      'marked@5.1.1', 
      'highlight.js@11.8.0',
      'node-emoji@1.11.0',
      'commander@9.4.1',
      'chalk@4.1.2',
      'fs-extra@10.1.0'
    ];
  }

  async checkNodeVersion() {
    console.log('🔍 Checking Node.js version...');
    const version = process.version;
    const majorVersion = parseInt(version.slice(1).split('.')[0]);
    
    if (majorVersion < 16) {
      throw new Error(`Node.js 16+ required, current version: ${version}`);
    }
    
    console.log(`✅ Node.js version: ${version}`);
    return true;
  }

  async installDependencies() {
    console.log('📦 Installing dependencies...');
    console.log('This may take a few minutes, especially for Puppeteer...\n');
    
    return new Promise((resolve, reject) => {
      // 使用更稳定的版本和选项
      const npm = spawn('npm', ['install', '--no-optional', '--no-audit'], {
        stdio: 'inherit',
        shell: true
      });
      
      npm.on('close', (code) => {
        if (code === 0) {
          console.log('\n✅ Dependencies installed successfully!');
          resolve();
        } else {
          reject(new Error(`npm install failed with code ${code}`));
        }
      });
      
      npm.on('error', (error) => {
        reject(new Error(`Failed to start npm: ${error.message}`));
      });
    });
  }

  async installDependenciesOneByOne() {
    console.log('📦 Installing dependencies one by one...');
    
    for (const dep of this.dependencies) {
      try {
        console.log(`Installing ${dep}...`);
        await this.installSingleDependency(dep);
        console.log(`✅ ${dep} installed`);
      } catch (error) {
        console.warn(`⚠️ Failed to install ${dep}: ${error.message}`);
        // 继续安装其他依赖
      }
    }
  }

  installSingleDependency(dependency) {
    return new Promise((resolve, reject) => {
      const npm = spawn('npm', ['install', dependency, '--no-optional'], {
        stdio: 'pipe',
        shell: true
      });
      
      let output = '';
      npm.stdout.on('data', (data) => {
        output += data.toString();
      });
      
      npm.stderr.on('data', (data) => {
        output += data.toString();
      });
      
      npm.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Installation failed: ${output}`));
        }
      });
    });
  }

  async testInstallation() {
    console.log('\n🧪 Testing installation...');
    
    const testResults = [];
    
    // 测试核心模块
    const modules = ['puppeteer', 'marked', 'highlight.js', 'commander', 'chalk'];
    
    for (const module of modules) {
      try {
        require(module);
        console.log(`✅ ${module} - OK`);
        testResults.push({ module, status: 'OK' });
      } catch (error) {
        console.log(`❌ ${module} - FAILED`);
        testResults.push({ module, status: 'FAILED', error: error.message });
      }
    }
    
    const failed = testResults.filter(r => r.status === 'FAILED');
    
    if (failed.length === 0) {
      console.log('\n🎉 All modules loaded successfully!');
      return true;
    } else {
      console.log(`\n⚠️ ${failed.length} modules failed to load:`);
      failed.forEach(f => console.log(`   - ${f.module}: ${f.error}`));
      return false;
    }
  }

  async runSimpleTest() {
    console.log('\n🚀 Running simple conversion test...');
    
    try {
      const SimpleConverter = require('./simple-converter');
      const converter = new SimpleConverter();
      
      // 创建测试Markdown内容
      const testContent = `# Test Document

This is a **test** document with *italic* text and \`code\`.

## Features
- Lists work
- **Bold** text
- *Italic* text

| Feature | Status |
|---------|--------|
| Tables  | ✅     |
| Lists   | ✅     |
`;
      
      // 写入测试文件
      const testPath = path.join(__dirname, 'test-input.md');
      fs.writeFileSync(testPath, testContent, 'utf8');
      
      // 转换
      const result = await converter.convertFile(testPath, 'test-output.pdf');
      
      // 清理测试文件
      if (fs.existsSync(testPath)) {
        fs.unlinkSync(testPath);
      }
      
      console.log('✅ Simple converter test passed!');
      return true;
      
    } catch (error) {
      console.log('❌ Simple converter test failed:', error.message);
      return false;
    }
  }

  async install() {
    try {
      console.log('🚀 MD2PDF Converter Installation\n');
      
      // 检查Node.js版本
      await this.checkNodeVersion();
      
      // 尝试安装依赖
      try {
        await this.installDependencies();
      } catch (error) {
        console.log('\n⚠️ Batch installation failed, trying one by one...');
        await this.installDependenciesOneByOne();
      }
      
      // 测试安装
      const testPassed = await this.testInstallation();
      
      // 运行简单测试
      await this.runSimpleTest();
      
      console.log('\n📋 Installation Summary:');
      console.log('========================');
      
      if (testPassed) {
        console.log('✅ Full converter available');
        console.log('   Usage: npm run cli input.md output.pdf');
        console.log('   Or: node bin/cli.js convert input.md output.pdf');
      } else {
        console.log('⚠️ Full converter not available (some dependencies missing)');
      }
      
      console.log('✅ Simple converter available');
      console.log('   Usage: node simple-converter.js input.md output.pdf');
      console.log('   (Generates HTML, manual PDF conversion needed)');
      
      console.log('\n🎯 Quick Start:');
      console.log('1. Test with sample: node simple-converter.js test/sample.md output/sample.pdf');
      console.log('2. Open generated HTML in browser and print to PDF');
      
      if (testPassed) {
        console.log('3. For automated PDF: npm run cli test/sample.md output/sample.pdf');
      }
      
    } catch (error) {
      console.error('\n❌ Installation failed:', error.message);
      console.log('\n🔧 Manual steps:');
      console.log('1. Ensure Node.js 16+ is installed');
      console.log('2. Run: npm install --no-optional');
      console.log('3. If that fails, use the simple converter: node simple-converter.js');
      process.exit(1);
    }
  }
}

// 运行安装
if (require.main === module) {
  const installer = new Installer();
  installer.install();
}

module.exports = Installer;
