/**
 * 修复版安装脚本 - 处理Puppeteer安装问题
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const https = require('https');

class InstallFixer {
  constructor() {
    this.alternatives = [
      {
        name: 'puppeteer-core + Chrome',
        packages: ['puppeteer-core@19.11.1', 'marked@5.1.1', 'highlight.js@11.8.0', 'commander@9.4.1', 'chalk@4.1.2', 'fs-extra@10.1.0'],
        description: '使用系统已安装的Chrome浏览器'
      },
      {
        name: 'playwright',
        packages: ['playwright@1.40.0', 'marked@5.1.1', 'highlight.js@11.8.0', 'commander@9.4.1', 'chalk@4.1.2', 'fs-extra@10.1.0'],
        description: '使用Playwright替代Puppeteer'
      },
      {
        name: 'minimal',
        packages: ['marked@5.1.1', 'highlight.js@11.8.0', 'commander@9.4.1', 'chalk@4.1.2'],
        description: '最小化安装，仅HTML生成功能'
      }
    ];
  }

  async checkNetworkConnection() {
    console.log('🌐 检查网络连接...');
    
    return new Promise((resolve) => {
      const req = https.get('https://registry.npmjs.org/', (res) => {
        console.log('✅ 网络连接正常');
        resolve(true);
      });
      
      req.on('error', (error) => {
        console.log('❌ 网络连接失败:', error.message);
        resolve(false);
      });
      
      req.setTimeout(5000, () => {
        console.log('❌ 网络连接超时');
        req.destroy();
        resolve(false);
      });
    });
  }

  async tryPuppeteerFixes() {
    console.log('🔧 尝试修复Puppeteer安装问题...\n');
    
    const fixes = [
      {
        name: '设置淘宝镜像',
        commands: [
          'npm config set registry https://registry.npmmirror.com',
          'npm config set puppeteer_download_host https://cdn.npmmirror.com/binaries'
        ]
      },
      {
        name: '跳过Chromium下载',
        commands: [
          'npm config set puppeteer_skip_chromium_download true'
        ]
      },
      {
        name: '清理npm缓存',
        commands: [
          'npm cache clean --force'
        ]
      }
    ];

    for (const fix of fixes) {
      console.log(`📝 ${fix.name}...`);
      
      for (const command of fix.commands) {
        try {
          await this.runCommand(command);
          console.log(`   ✅ ${command}`);
        } catch (error) {
          console.log(`   ❌ ${command} - ${error.message}`);
        }
      }
      console.log('');
    }
  }

  async installAlternative(alternativeIndex = 0) {
    const alternative = this.alternatives[alternativeIndex];
    console.log(`📦 尝试安装方案: ${alternative.name}`);
    console.log(`📝 说明: ${alternative.description}\n`);

    try {
      // 先安装基础包
      const basicPackages = alternative.packages.filter(pkg => !pkg.includes('puppeteer') && !pkg.includes('playwright'));
      
      console.log('安装基础依赖包...');
      for (const pkg of basicPackages) {
        try {
          await this.installSinglePackage(pkg);
          console.log(`✅ ${pkg}`);
        } catch (error) {
          console.log(`⚠️ ${pkg} - ${error.message}`);
        }
      }

      // 再安装浏览器相关包
      const browserPackages = alternative.packages.filter(pkg => pkg.includes('puppeteer') || pkg.includes('playwright'));
      
      if (browserPackages.length > 0) {
        console.log('\n安装浏览器相关包...');
        for (const pkg of browserPackages) {
          try {
            await this.installSinglePackage(pkg, true);
            console.log(`✅ ${pkg}`);
          } catch (error) {
            console.log(`❌ ${pkg} - ${error.message}`);
            throw error;
          }
        }
      }

      return true;
    } catch (error) {
      console.log(`❌ 方案 ${alternative.name} 安装失败:`, error.message);
      return false;
    }
  }

  async installSinglePackage(packageName, isBrowser = false) {
    return new Promise((resolve, reject) => {
      const args = ['install', packageName, '--no-audit', '--no-fund'];
      
      if (isBrowser) {
        args.push('--ignore-scripts'); // 跳过下载脚本
      }

      const npm = spawn('npm', args, {
        stdio: 'pipe',
        shell: true
      });

      let output = '';
      let errorOutput = '';

      npm.stdout.on('data', (data) => {
        output += data.toString();
      });

      npm.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      npm.on('close', (code) => {
        if (code === 0) {
          resolve(output);
        } else {
          reject(new Error(`安装失败 (code ${code}): ${errorOutput}`));
        }
      });

      npm.on('error', (error) => {
        reject(new Error(`无法启动npm: ${error.message}`));
      });
    });
  }

  runCommand(command) {
    return new Promise((resolve, reject) => {
      exec(command, (error, stdout, stderr) => {
        if (error) {
          reject(error);
        } else {
          resolve(stdout);
        }
      });
    });
  }

  async createAlternativeConverter() {
    console.log('🔧 创建替代转换器...');
    
    const converterCode = `
/**
 * 替代转换器 - 使用系统浏览器或其他方案
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

class AlternativeConverter {
  constructor() {
    this.hasChrome = false;
    this.hasEdge = false;
    this.checkBrowsers();
  }

  async checkBrowsers() {
    // 检查系统浏览器
    try {
      await this.runCommand('chrome --version');
      this.hasChrome = true;
      console.log('✅ 检测到Chrome浏览器');
    } catch (error) {
      try {
        await this.runCommand('google-chrome --version');
        this.hasChrome = true;
        console.log('✅ 检测到Google Chrome浏览器');
      } catch (error2) {
        console.log('⚠️ 未检测到Chrome浏览器');
      }
    }

    try {
      await this.runCommand('msedge --version');
      this.hasEdge = true;
      console.log('✅ 检测到Edge浏览器');
    } catch (error) {
      console.log('⚠️ 未检测到Edge浏览器');
    }
  }

  async convertWithSystemBrowser(htmlPath, pdfPath) {
    if (!this.hasChrome && !this.hasEdge) {
      throw new Error('未找到支持的浏览器，请手动在浏览器中打印为PDF');
    }

    const browser = this.hasChrome ? 'chrome' : 'msedge';
    const command = \`\${browser} --headless --disable-gpu --print-to-pdf="\${pdfPath}" "file://\${path.resolve(htmlPath)}"\`;

    try {
      await this.runCommand(command);
      console.log('✅ PDF生成成功:', pdfPath);
      return { success: true, outputPath: pdfPath };
    } catch (error) {
      throw new Error(\`浏览器转换失败: \${error.message}\`);
    }
  }

  runCommand(command) {
    return new Promise((resolve, reject) => {
      const child = spawn(command, { shell: true, stdio: 'pipe' });
      
      let output = '';
      child.stdout.on('data', (data) => output += data);
      child.stderr.on('data', (data) => output += data);
      
      child.on('close', (code) => {
        if (code === 0) resolve(output);
        else reject(new Error(\`Command failed with code \${code}: \${output}\`));
      });
    });
  }
}

module.exports = AlternativeConverter;
`;

    fs.writeFileSync('alternative-converter.js', converterCode, 'utf8');
    console.log('✅ 替代转换器创建完成: alternative-converter.js');
  }

  async testInstallation() {
    console.log('\n🧪 测试安装结果...');
    
    const testResults = [];
    const modules = ['marked', 'highlight.js', 'commander', 'chalk'];
    
    for (const module of modules) {
      try {
        require(module);
        console.log(`✅ ${module} - 可用`);
        testResults.push({ module, status: 'OK' });
      } catch (error) {
        console.log(`❌ ${module} - 不可用`);
        testResults.push({ module, status: 'FAILED' });
      }
    }

    // 测试浏览器自动化
    let browserAutomation = false;
    try {
      require('puppeteer-core');
      console.log('✅ puppeteer-core - 可用');
      browserAutomation = true;
    } catch (error) {
      try {
        require('playwright');
        console.log('✅ playwright - 可用');
        browserAutomation = true;
      } catch (error2) {
        console.log('⚠️ 无浏览器自动化库，将使用系统浏览器');
      }
    }

    return {
      basicModules: testResults.filter(r => r.status === 'OK').length,
      totalModules: testResults.length,
      browserAutomation
    };
  }

  async install() {
    console.log('🚀 MD2PDF 修复安装程序\n');

    try {
      // 检查网络
      const hasNetwork = await this.checkNetworkConnection();
      if (!hasNetwork) {
        console.log('❌ 网络连接问题，请检查网络设置');
        return;
      }

      // 尝试修复Puppeteer问题
      await this.tryPuppeteerFixes();

      // 尝试不同的安装方案
      let installSuccess = false;
      
      for (let i = 0; i < this.alternatives.length; i++) {
        console.log(`\n📋 尝试方案 ${i + 1}/${this.alternatives.length}`);
        
        try {
          const success = await this.installAlternative(i);
          if (success) {
            installSuccess = true;
            break;
          }
        } catch (error) {
          console.log(`方案 ${i + 1} 失败，尝试下一个方案...`);
        }
      }

      // 创建替代转换器
      await this.createAlternativeConverter();

      // 测试安装结果
      const testResult = await this.testInstallation();

      console.log('\n📊 安装结果总结:');
      console.log('==================');
      console.log(\`基础模块: \${testResult.basicModules}/\${testResult.totalModules} 可用\`);
      console.log(\`浏览器自动化: \${testResult.browserAutomation ? '✅ 可用' : '❌ 不可用'}\`);

      console.log('\n🎯 可用的转换方案:');
      console.log('1. ✅ 简化版转换器: node simple-converter.js input.md output.pdf');
      
      if (testResult.basicModules >= 3) {
        console.log('2. ✅ 增强版转换器: node enhanced-converter.js input.md output.pdf');
      }
      
      if (testResult.browserAutomation) {
        console.log('3. ✅ 全自动转换器: npm run cli input.md output.pdf');
      } else {
        console.log('3. ⚠️ 全自动转换器: 需要手动安装浏览器自动化库');
      }

      console.log('\n💡 推荐使用简化版转换器，稳定可靠且无需复杂依赖！');

    } catch (error) {
      console.error('\n❌ 安装过程出错:', error.message);
      console.log('\n🔧 手动解决方案:');
      console.log('1. 使用简化版: node simple-converter.js input.md output.pdf');
      console.log('2. 手动安装: npm install marked highlight.js commander chalk --save');
      console.log('3. 如需PDF自动化，考虑使用在线转换服务');
    }
  }
}

// 运行修复安装
if (require.main === module) {
  const fixer = new InstallFixer();
  fixer.install();
}

module.exports = InstallFixer;
`;

    fs.writeFileSync('install-fix.js', converterCode, 'utf8');
    console.log('✅ 修复安装脚本创建完成');
  }
}

module.exports = InstallFixer;
