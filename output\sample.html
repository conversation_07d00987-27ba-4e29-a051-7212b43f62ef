<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown Document</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        background: white;
      }
      
      h1, h2, h3, h4, h5, h6 {
        margin-top: 2rem;
        margin-bottom: 1rem;
        font-weight: 600;
        line-height: 1.25;
        color: #1a1a1a;
      }
      
      h1 { font-size: 2.5rem; border-bottom: 3px solid #e1e4e8; padding-bottom: 0.5rem; }
      h2 { font-size: 2rem; border-bottom: 2px solid #e1e4e8; padding-bottom: 0.3rem; }
      h3 { font-size: 1.5rem; }
      
      p { margin-bottom: 1rem; text-align: justify; }
      
      code {
        background-color: #f3f4f6;
        padding: 0.2rem 0.4rem;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
        font-size: 0.9em;
        color: #e83e8c;
      }
      
      pre {
        background-color: #f8f9fa;
        border: 1px solid #e1e4e8;
        border-radius: 6px;
        padding: 1rem;
        margin: 1rem 0;
        overflow-x: auto;
      }
      
      pre code {
        background: none;
        padding: 0;
        color: #24292e;
        font-size: 0.85rem;
      }
      
      .markdown-table {
        width: 100%;
        border-collapse: collapse;
        border-spacing: 0;
        border: 1px solid #e1e4e8;
        border-radius: 6px;
        overflow: hidden;
        margin: 1.5rem 0;
      }
      
      .markdown-table th,
      .markdown-table td {
        padding: 12px 16px;
        text-align: left;
        border-bottom: 1px solid #e1e4e8;
        border-right: 1px solid #e1e4e8;
      }
      
      .markdown-table th {
        background-color: #f6f8fa;
        font-weight: 600;
        color: #24292e;
      }
      
      .markdown-table tr:nth-child(even) {
        background-color: #f9f9f9;
      }
      
      ul, ol { margin-bottom: 1rem; padding-left: 2rem; }
      li { margin-bottom: 0.25rem; }
      
      blockquote {
        margin: 1rem 0;
        padding: 0 1rem;
        color: #6a737d;
        border-left: 4px solid #dfe2e5;
        background-color: #f8f9fa;
        border-radius: 0 6px 6px 0;
      }
      
      a { color: #0366d6; text-decoration: none; }
      a:hover { text-decoration: underline; }
      
      hr {
        height: 2px;
        background-color: #e1e4e8;
        border: none;
        margin: 2rem 0;
      }
      
      img {
        max-width: 100%;
        height: auto;
        border-radius: 6px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        margin: 1rem 0;
        display: block;
      }
    </style>
</head>
<body>
    <div class="markdown-body">
        <h1>Markdown转PDF测试文档 📄</h1><p>这是一个测试文档，用于验证Markdown转PDF转换器的各种功能。</p><h2>基本文本格式</h2><p>这是一个普通段落，包含<strong>粗体文本</strong>、<em>斜体文本</em>和<code>行内代码</code>。</p><h3>列表测试</h3><p>#### 无序列表
- 第一项 ✅
- 第二项 🎯
- 第三项包含子列表：
  - 子项目1
  - 子项目2
  - 子项目3</p><p>#### 有序列表
<li>首先做这个 🚀</li>
<li>然后做那个 ⭐</li>
<li>最后完成这个 🎉</li></p><h2>表格测试</h2><p>| 功能 | 状态 | 描述 | 优先级 |
|------|------|------|--------|
| Markdown解析 | ✅ 完成 | 支持标准语法 | 高 |
| 表格渲染 | ✅ 完成 | 美观的表格样式 | 高 |
| 表情符号 | ✅ 完成 | 支持Unicode表情 | 中 |
| 图片支持 | ✅ 完成 | 本地和网络图片 | 高 |
| 代码高亮 | ✅ 完成 | 多语言语法高亮 | 中 |</p><h2>代码块测试</h2><h3>JavaScript代码</h3>
<code></code>`javascript
// 示例JavaScript代码
function convertMarkdownToPdf(input, output) {
  const converter = new MD2PDFConverter();
  return converter.convert(input, output, {
    format: 'A4',
    margin: { top: '1cm', bottom: '1cm' }
  });
}</p><p>// 使用示例
convertMarkdownToPdf('input.md', 'output.pdf')
  .then(result => console.log('转换成功！', result))
  .catch(error => console.error('转换失败：', error));
<code></code>`</p><h3>Python代码</h3>
<code></code>`python
<h1>示例Python代码</h1>
import os
from pathlib import Path</p><p>def process_markdown_files(directory):
    """处理目录中的所有Markdown文件"""
    md_files = Path(directory).glob('*.md')
    
    for file_path in md_files:
        print(f"处理文件: {file_path}")
        # 这里可以调用转换逻辑
        convert_to_pdf(file_path)</p><h1>使用示例</h1>
if __name__ == "__main__":
    process_markdown_files("./documents")
<code></code>`</p><h3>CSS样式</h3>
<code></code>`css
/<em> 自定义PDF样式 </em>/
.markdown-table {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
}</p><p>.markdown-table th,
.markdown-table td {
  border: 1px solid #ddd;
  padding: 12px;
  text-align: left;
}</p><p>.markdown-table th {
  background-color: #f2f2f2;
  font-weight: bold;
}
<code></code>`</p><h2>引用块测试</h2><blockquote>这是一个引用块的示例。引用块通常用于突出显示重要信息或引用他人的话。</blockquote>
> 
<blockquote>支持多行引用，并且可以包含其他Markdown元素，比如<strong>粗体</strong>和<em>斜体</em>。</blockquote><h2>链接测试</h2><p>- <a href="https://github.com/example/md2pdf">GitHub项目地址</a>
- <a href="https://docs.example.com">官方文档</a>
- <a href="https://demo.example.com">在线演示</a></p><h2>表情符号测试 😊</h2><p>这个转换器支持各种表情符号：</p><p>- 开心：😊 😄 😃 🙂
- 工作：💻 📝 📊 📈
- 成功：✅ 🎉 🎯 ⭐
- 警告：⚠️ ❌ 🚨 ⛔
- 自然：🌟 🌈 🌸 🍀</p><h2>分割线测试</h2><hr><h2>图片测试</h2><p>由于这是示例文件，图片路径可能不存在，但转换器会优雅地处理：</p><p>!<a href="./images/sample.png">示例图片</a></p><p><em>注意：如果图片不存在，转换器会显示警告但不会中断转换过程。</em></p><h2>数学公式（如果支持）</h2><p>虽然基础版本可能不支持LaTeX数学公式，但可以通过扩展添加：</p><p><code></code>`
E = mc²
∑(i=1 to n) xi = x1 + x2 + ... + xn
<code></code>`</p><h2>总结</h2><p>这个Markdown转PDF转换器具有以下特点：
<li><strong>功能完整</strong> - 支持标准Markdown语法</li>
<li><strong>样式美观</strong> - 提供多种预设模板</li>
<li><strong>扩展性强</strong> - 支持自定义CSS样式</li>
<li><strong>易于使用</strong> - 提供命令行和编程接口</li>
<li><strong>性能优秀</strong> - 基于Puppeteer的高效转换</li></p><h3>使用建议</h3><p>- 对于简单文档，使用默认设置即可
- 对于学术论文，推荐使用<code>academic</code>模板
- 对于技术文档，推荐使用<code>github</code>模板
- 需要自定义样式时，可以提供CSS文件</p><hr><p><strong>转换完成时间</strong>: $(date)  
<strong>文档版本</strong>: v1.0  
<strong>转换器版本</strong>: MD2PDF v1.0.0</p><p><table class="markdown-table"><thead><tr><th>功能</th><th>状态</th><th>描述</th><th>优先级</th></tr></thead><tbody><tr><td>Markdown解析</td><td>✅ 完成</td><td>支持标准语法</td><td>高</td></tr><tr><td>表格渲染</td><td>✅ 完成</td><td>美观的表格样式</td><td>高</td></tr><tr><td>表情符号</td><td>✅ 完成</td><td>支持Unicode表情</td><td>中</td></tr><tr><td>图片支持</td><td>✅ 完成</td><td>本地和网络图片</td><td>高</td></tr><tr><td>代码高亮</td><td>✅ 完成</td><td>多语言语法高亮</td><td>中</td></tr></tbody></table></p>
    </div>
</body>
</html>