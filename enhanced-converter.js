/**
 * 增强版Markdown转PDF转换器
 * 无需Puppeteer，支持更多功能
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

class EnhancedMarkdownConverter {
  constructor() {
    this.templates = this.getTemplates();
    this.currentTemplate = 'default';
  }

  // 增强的Markdown解析器
  parseMarkdown(content) {
    let html = content;
    
    // 处理表情符号（简单替换）
    const emojiMap = {
      ':smile:': '😊', ':heart:': '❤️', ':star:': '⭐', ':check:': '✅',
      ':x:': '❌', ':warning:': '⚠️', ':rocket:': '🚀', ':book:': '📖',
      ':computer:': '💻', ':bulb:': '💡', ':fire:': '🔥', ':tada:': '🎉'
    };
    
    Object.entries(emojiMap).forEach(([key, value]) => {
      html = html.replace(new RegExp(key, 'g'), value);
    });
    
    // 处理表格（改进版）
    html = this.parseAdvancedTables(html);
    
    // 处理代码块（带语言标识）
    html = html.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
      const language = lang || 'text';
      return `<pre class="code-block language-${language}"><code class="language-${language}">${this.escapeHtml(code.trim())}</code></pre>`;
    });
    
    // 处理标题（带锚点）
    html = html.replace(/^(#{1,6})\s+(.+)$/gm, (match, hashes, title) => {
      const level = hashes.length;
      const id = title.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-');
      return `<h${level} id="${id}">${title}</h${level}>`;
    });
    
    // 处理粗体和斜体
    html = html.replace(/\*\*\*(.*?)\*\*\*/g, '<strong><em>$1</em></strong>');
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
    
    // 处理删除线
    html = html.replace(/~~(.*?)~~/g, '<del>$1</del>');
    
    // 处理行内代码
    html = html.replace(/`([^`]+)`/g, '<code>$1</code>');
    
    // 处理链接
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');
    
    // 处理图片
    html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" class="markdown-image">');
    
    // 处理任务列表
    html = html.replace(/^\s*-\s+\[([x\s])\]\s+(.+)$/gm, (match, checked, text) => {
      const isChecked = checked.toLowerCase() === 'x';
      return `<li class="task-item"><input type="checkbox" ${isChecked ? 'checked' : ''} disabled> ${text}</li>`;
    });
    
    // 处理普通列表
    html = html.replace(/^\s*[-*+]\s+(.+)$/gm, '<li>$1</li>');
    html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');
    
    // 处理有序列表
    html = html.replace(/^\s*\d+\.\s+(.+)$/gm, '<li>$1</li>');
    
    // 处理引用
    html = html.replace(/^>\s+(.+)$/gm, '<blockquote>$1</blockquote>');
    
    // 处理分割线
    html = html.replace(/^---+$/gm, '<hr>');
    
    // 处理段落
    html = html.split('\n\n').map(paragraph => {
      paragraph = paragraph.trim();
      if (!paragraph) return '';
      
      // 跳过已经是HTML标签的内容
      if (paragraph.match(/^<(h[1-6]|ul|ol|blockquote|hr|pre|table)/)) {
        return paragraph;
      }
      
      return `<p>${paragraph}</p>`;
    }).join('\n');
    
    // 清理多余的标签
    html = this.cleanupHtml(html);
    
    return html;
  }

  // 高级表格解析
  parseAdvancedTables(content) {
    const lines = content.split('\n');
    let result = [];
    let i = 0;
    
    while (i < lines.length) {
      const line = lines[i].trim();
      
      // 检测表格开始
      if (line.includes('|') && !line.startsWith('|---')) {
        const tableData = [];
        let j = i;
        
        // 收集表格行
        while (j < lines.length && lines[j].trim().includes('|')) {
          const row = lines[j].trim();
          if (!row.startsWith('|---')) {
            const cells = row.split('|').map(cell => cell.trim()).filter(cell => cell);
            if (cells.length > 0) {
              tableData.push(cells);
            }
          }
          j++;
        }
        
        if (tableData.length > 0) {
          result.push(this.generateAdvancedTable(tableData));
          i = j;
          continue;
        }
      }
      
      result.push(lines[i]);
      i++;
    }
    
    return result.join('\n');
  }

  generateAdvancedTable(data) {
    if (data.length === 0) return '';
    
    let html = '<div class="table-container"><table class="markdown-table">';
    
    // 表头
    html += '<thead><tr>';
    data[0].forEach(cell => {
      html += `<th>${cell}</th>`;
    });
    html += '</tr></thead>';
    
    // 表体
    if (data.length > 1) {
      html += '<tbody>';
      for (let i = 1; i < data.length; i++) {
        html += '<tr>';
        data[i].forEach((cell, index) => {
          // 处理单元格内的Markdown
          const processedCell = cell
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`([^`]+)`/g, '<code>$1</code>');
          html += `<td>${processedCell}</td>`;
        });
        html += '</tr>';
      }
      html += '</tbody>';
    }
    
    html += '</table></div>';
    return html;
  }

  cleanupHtml(html) {
    // 清理空段落和多余的换行
    html = html.replace(/<p><\/p>/g, '');
    html = html.replace(/<p>(<h[1-6]>)/g, '$1');
    html = html.replace(/(<\/h[1-6]>)<\/p>/g, '$1');
    html = html.replace(/<p>(<ul>|<ol>|<blockquote>|<hr>|<pre>|<div)/g, '$1');
    html = html.replace(/(<\/ul>|<\/ol>|<\/blockquote>|<\/pre>|<\/div>)<\/p>/g, '$1');
    html = html.replace(/\n{3,}/g, '\n\n');
    
    return html;
  }

  escapeHtml(text) {
    const map = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, m => map[m]);
  }

  // 模板系统
  getTemplates() {
    return {
      default: {
        name: '默认样式',
        css: this.getDefaultCSS()
      },
      github: {
        name: 'GitHub样式',
        css: this.getGitHubCSS()
      },
      academic: {
        name: '学术论文样式',
        css: this.getAcademicCSS()
      },
      minimal: {
        name: '简约样式',
        css: this.getMinimalCSS()
      }
    };
  }

  getDefaultCSS() {
    return `
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        background: white;
      }
      
      h1, h2, h3, h4, h5, h6 {
        margin-top: 2rem;
        margin-bottom: 1rem;
        font-weight: 600;
        line-height: 1.25;
        color: #1a1a1a;
      }
      
      h1 { font-size: 2.5rem; border-bottom: 3px solid #e1e4e8; padding-bottom: 0.5rem; }
      h2 { font-size: 2rem; border-bottom: 2px solid #e1e4e8; padding-bottom: 0.3rem; }
      h3 { font-size: 1.5rem; }
      h4 { font-size: 1.25rem; }
      h5 { font-size: 1.1rem; }
      h6 { font-size: 1rem; color: #6a737d; }
      
      p { margin-bottom: 1rem; text-align: justify; }
      
      code {
        background-color: #f3f4f6;
        padding: 0.2rem 0.4rem;
        border-radius: 3px;
        font-family: 'Courier New', 'Consolas', monospace;
        font-size: 0.9em;
        color: #e83e8c;
      }
      
      .code-block {
        background-color: #f8f9fa;
        border: 1px solid #e1e4e8;
        border-radius: 6px;
        padding: 1rem;
        margin: 1rem 0;
        overflow-x: auto;
        position: relative;
      }
      
      .code-block::before {
        content: attr(class);
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        font-size: 0.7rem;
        color: #6a737d;
        text-transform: uppercase;
      }
      
      .code-block code {
        background: none;
        padding: 0;
        color: #24292e;
        font-size: 0.85rem;
        line-height: 1.45;
      }
      
      .table-container {
        margin: 1.5rem 0;
        overflow-x: auto;
      }
      
      .markdown-table {
        width: 100%;
        border-collapse: collapse;
        border-spacing: 0;
        border: 1px solid #e1e4e8;
        border-radius: 6px;
        overflow: hidden;
      }
      
      .markdown-table th,
      .markdown-table td {
        padding: 12px 16px;
        text-align: left;
        border-bottom: 1px solid #e1e4e8;
        border-right: 1px solid #e1e4e8;
      }
      
      .markdown-table th {
        background-color: #f6f8fa;
        font-weight: 600;
        color: #24292e;
      }
      
      .markdown-table tr:nth-child(even) {
        background-color: #f9f9f9;
      }
      
      .markdown-table tr:hover {
        background-color: #f5f5f5;
      }
      
      ul, ol { margin-bottom: 1rem; padding-left: 2rem; }
      li { margin-bottom: 0.25rem; }
      
      .task-item {
        list-style: none;
        margin-left: -1.5rem;
      }
      
      .task-item input[type="checkbox"] {
        margin-right: 0.5rem;
      }
      
      blockquote {
        margin: 1rem 0;
        padding: 0 1rem;
        color: #6a737d;
        border-left: 4px solid #dfe2e5;
        background-color: #f8f9fa;
        border-radius: 0 6px 6px 0;
      }
      
      a { color: #0366d6; text-decoration: none; }
      a:hover { text-decoration: underline; }
      
      hr {
        height: 2px;
        background-color: #e1e4e8;
        border: none;
        margin: 2rem 0;
      }
      
      .markdown-image {
        max-width: 100%;
        height: auto;
        border-radius: 6px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        margin: 1rem 0;
        display: block;
      }
      
      del {
        color: #6a737d;
        text-decoration: line-through;
      }
      
      @media print {
        body { max-width: none; margin: 0; padding: 1cm; }
        .code-block { page-break-inside: avoid; }
        .markdown-table { page-break-inside: avoid; }
        h1, h2, h3, h4, h5, h6 { page-break-after: avoid; }
      }
    `;
  }

  getGitHubCSS() {
    return this.getDefaultCSS() + `
      body {
        font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Helvetica,Arial,sans-serif;
        background-color: #ffffff;
      }
      
      h1, h2 {
        border-bottom: 1px solid #eaecef;
      }
      
      .code-block {
        background-color: #f6f8fa;
        border: 1px solid #d0d7de;
      }
    `;
  }

  getAcademicCSS() {
    return `
      body {
        font-family: "Times New Roman", Times, serif;
        line-height: 1.8;
        font-size: 12pt;
        color: #000;
        max-width: 210mm;
        margin: 0 auto;
        padding: 2.5cm;
        background: white;
      }
      
      h1, h2, h3, h4, h5, h6 {
        font-family: Arial, sans-serif;
        font-weight: bold;
        margin-top: 1.5em;
        margin-bottom: 0.5em;
      }
      
      h1 { font-size: 18pt; text-align: center; }
      h2 { font-size: 16pt; }
      h3 { font-size: 14pt; }
      
      p { text-align: justify; margin-bottom: 1em; }
      
      .markdown-table {
        font-size: 10pt;
        margin: 1em auto;
      }
      
      .code-block {
        font-size: 10pt;
        background-color: #f9f9f9;
        border: 1px solid #ccc;
      }
    `;
  }

  getMinimalCSS() {
    return `
      body {
        font-family: Georgia, serif;
        line-height: 1.7;
        color: #2c3e50;
        max-width: 650px;
        margin: 0 auto;
        padding: 2rem;
        background: #ffffff;
      }
      
      h1, h2, h3, h4, h5, h6 {
        color: #34495e;
        font-weight: normal;
        margin-top: 2em;
        margin-bottom: 1em;
      }
      
      h1 { font-size: 2em; border-bottom: 1px solid #bdc3c7; }
      h2 { font-size: 1.5em; }
      h3 { font-size: 1.2em; }
      
      .markdown-table {
        border: none;
        border-top: 2px solid #34495e;
        border-bottom: 2px solid #34495e;
      }
      
      .markdown-table th {
        background: none;
        border-bottom: 1px solid #34495e;
        font-weight: bold;
      }
      
      .markdown-table td {
        border: none;
        border-bottom: 1px solid #ecf0f1;
      }
    `;
  }

  // 生成完整HTML
  generateHTML(markdownContent, options = {}) {
    const template = options.template || this.currentTemplate;
    const title = options.title || this.extractTitle(markdownContent) || 'Markdown Document';
    
    const htmlContent = this.parseMarkdown(markdownContent);
    const css = this.templates[template]?.css || this.templates.default.css;
    
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${this.escapeHtml(title)}</title>
    <style>${css}</style>
</head>
<body>
    <div class="markdown-body">
        ${htmlContent}
    </div>
</body>
</html>`;
  }

  extractTitle(content) {
    const match = content.match(/^#\s+(.+)$/m);
    return match ? match[1] : null;
  }

  // 转换文件
  async convertFile(inputPath, outputPath, options = {}) {
    try {
      console.log('📖 读取Markdown文件...');
      const markdownContent = fs.readFileSync(inputPath, 'utf8');
      
      console.log('🔄 转换为HTML...');
      const html = this.generateHTML(markdownContent, options);
      
      // 保存HTML文件
      const htmlPath = outputPath.replace('.pdf', '.html');
      fs.writeFileSync(htmlPath, html, 'utf8');
      
      console.log('✅ HTML文件生成成功:', htmlPath);
      
      // 尝试自动转换为PDF（如果系统支持）
      if (options.autoPdf !== false) {
        try {
          await this.tryAutoPdfConversion(htmlPath, outputPath);
        } catch (error) {
          console.log('⚠️ 自动PDF转换失败，请手动转换:', error.message);
        }
      }
      
      console.log('');
      console.log('📋 转换完成！');
      console.log('🌐 HTML文件:', htmlPath);
      console.log('📄 手动转PDF步骤:');
      console.log('   1. 在浏览器中打开HTML文件');
      console.log('   2. 按 Ctrl+P (或 Cmd+P)');
      console.log('   3. 选择"保存为PDF"');
      console.log('   4. 调整设置并保存');
      
      return {
        success: true,
        htmlPath,
        outputPath,
        template: options.template || this.currentTemplate
      };
      
    } catch (error) {
      console.error('❌ 转换失败:', error.message);
      throw error;
    }
  }

  async tryAutoPdfConversion(htmlPath, pdfPath) {
    // 尝试使用系统浏览器转换
    const browsers = [
      'chrome --headless --disable-gpu --print-to-pdf',
      'google-chrome --headless --disable-gpu --print-to-pdf',
      'msedge --headless --disable-gpu --print-to-pdf'
    ];
    
    for (const browser of browsers) {
      try {
        const command = `${browser}="${pdfPath}" "file://${path.resolve(htmlPath)}"`;
        await this.runCommand(command);
        console.log('✅ 自动PDF转换成功:', pdfPath);
        return true;
      } catch (error) {
        // 继续尝试下一个浏览器
      }
    }
    
    throw new Error('未找到支持的浏览器进行自动转换');
  }

  runCommand(command) {
    return new Promise((resolve, reject) => {
      const child = spawn(command, { shell: true, stdio: 'pipe' });
      
      let output = '';
      child.stdout.on('data', (data) => output += data);
      child.stderr.on('data', (data) => output += data);
      
      child.on('close', (code) => {
        if (code === 0) resolve(output);
        else reject(new Error(`Command failed: ${output}`));
      });
      
      child.on('error', (error) => {
        reject(error);
      });
    });
  }

  // 设置模板
  setTemplate(templateName) {
    if (this.templates[templateName]) {
      this.currentTemplate = templateName;
      console.log(`✅ 已切换到模板: ${this.templates[templateName].name}`);
    } else {
      console.log(`❌ 未知模板: ${templateName}`);
      console.log('可用模板:', Object.keys(this.templates).join(', '));
    }
  }

  // 列出可用模板
  listTemplates() {
    console.log('📋 可用模板:');
    Object.entries(this.templates).forEach(([key, template]) => {
      const current = key === this.currentTemplate ? ' (当前)' : '';
      console.log(`   ${key}: ${template.name}${current}`);
    });
  }
}

// 命令行使用
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length < 2) {
    console.log('使用方法: node enhanced-converter.js <input.md> <output.pdf> [template]');
    console.log('示例: node enhanced-converter.js test/sample.md output/sample.pdf github');
    console.log('');
    console.log('可用模板: default, github, academic, minimal');
    process.exit(1);
  }
  
  const [inputPath, outputPath, template] = args;
  const converter = new EnhancedMarkdownConverter();
  
  const options = {};
  if (template) {
    options.template = template;
  }
  
  converter.convertFile(inputPath, outputPath, options)
    .then(result => {
      console.log('🎉 转换完成!');
    })
    .catch(error => {
      console.error('💥 错误:', error.message);
      process.exit(1);
    });
}

module.exports = EnhancedMarkdownConverter;
