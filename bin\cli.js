#!/usr/bin/env node

const { Command } = require('commander');
const chalk = require('chalk');
const path = require('path');
const fs = require('fs-extra');
const { MD2PDFConverter } = require('../src/index');

const program = new Command();

program
  .name('md2pdf')
  .description('Convert Markdown files to PDF with support for tables, emojis, and images')
  .version('1.0.0');

// 主转换命令
program
  .command('convert <input> <output>')
  .alias('c')
  .description('Convert a Markdown file to PDF')
  .option('-f, --format <format>', 'PDF format (A4, A3, A5, Legal, Letter)', 'A4')
  .option('-m, --margin <margin>', 'Page margins (e.g., "1cm" or "top:2cm,bottom:1cm")', '1cm')
  .option('-t, --template <template>', 'Template preset (github, academic, minimal)', 'default')
  .option('--css <file>', 'Custom CSS file path')
  .option('--no-background', 'Disable background printing')
  .option('--header <template>', 'Header template HTML')
  .option('--footer <template>', 'Footer template HTML')
  .option('--timeout <ms>', 'Conversion timeout in milliseconds', '30000')
  .action(async (input, output, options) => {
    try {
      console.log(chalk.blue('🚀 Starting Markdown to PDF conversion...'));
      
      // 验证输入文件
      if (!await fs.pathExists(input)) {
        console.error(chalk.red(`❌ Input file not found: ${input}`));
        process.exit(1);
      }
      
      // 解析选项
      const converterOptions = parseOptions(options);
      
      // 创建转换器
      const converter = new MD2PDFConverter(converterOptions);
      
      // 使用预设模板
      if (options.template && options.template !== 'default') {
        try {
          converter.usePresetTemplate(options.template);
        } catch (error) {
          console.warn(chalk.yellow(`⚠️ Unknown template: ${options.template}, using default`));
        }
      }
      
      // 加载自定义CSS
      if (options.css) {
        if (await fs.pathExists(options.css)) {
          const customCSS = await fs.readFile(options.css, 'utf8');
          converter.setCustomCSS(customCSS);
        } else {
          console.warn(chalk.yellow(`⚠️ CSS file not found: ${options.css}`));
        }
      }
      
      // 执行转换
      const result = await converter.convert(input, output, converterOptions);
      
      // 显示结果
      console.log(chalk.green('✅ Conversion completed successfully!'));
      console.log(chalk.cyan(`📁 Input: ${result.inputPath}`));
      console.log(chalk.cyan(`📄 Output: ${result.outputPath}`));
      console.log(chalk.cyan(`📊 File size: ${(result.fileSize / 1024).toFixed(2)} KB`));
      
      if (result.metadata.title) {
        console.log(chalk.cyan(`📝 Title: ${result.metadata.title}`));
      }
      
    } catch (error) {
      console.error(chalk.red('❌ Conversion failed:'), error.message);
      process.exit(1);
    }
  });

// 批量转换命令
program
  .command('batch <inputDir> <outputDir>')
  .alias('b')
  .description('Convert all Markdown files in a directory to PDF')
  .option('-f, --format <format>', 'PDF format', 'A4')
  .option('-m, --margin <margin>', 'Page margins', '1cm')
  .option('-t, --template <template>', 'Template preset', 'default')
  .option('--css <file>', 'Custom CSS file path')
  .action(async (inputDir, outputDir, options) => {
    try {
      console.log(chalk.blue('🔄 Starting batch conversion...'));
      
      if (!await fs.pathExists(inputDir)) {
        console.error(chalk.red(`❌ Input directory not found: ${inputDir}`));
        process.exit(1);
      }
      
      const converterOptions = parseOptions(options);
      const converter = new MD2PDFConverter(converterOptions);
      
      if (options.template && options.template !== 'default') {
        try {
          converter.usePresetTemplate(options.template);
        } catch (error) {
          console.warn(chalk.yellow(`⚠️ Unknown template: ${options.template}`));
        }
      }
      
      const results = await converter.convertBatch(inputDir, outputDir, converterOptions);
      
      const successful = results.filter(r => r.success).length;
      const total = results.length;
      
      console.log(chalk.green(`✅ Batch conversion completed: ${successful}/${total} files converted`));
      
      results.forEach(result => {
        if (result.success) {
          console.log(chalk.green(`  ✓ ${path.basename(result.inputPath)} → ${path.basename(result.outputPath)}`));
        } else {
          console.log(chalk.red(`  ✗ ${path.basename(result.inputPath)}: ${result.error}`));
        }
      });
      
    } catch (error) {
      console.error(chalk.red('❌ Batch conversion failed:'), error.message);
      process.exit(1);
    }
  });

// 预览命令
program
  .command('preview <input>')
  .alias('p')
  .description('Generate HTML preview of Markdown file')
  .option('-o, --output <file>', 'Output HTML file path')
  .option('-t, --template <template>', 'Template preset', 'default')
  .option('--css <file>', 'Custom CSS file path')
  .action(async (input, options) => {
    try {
      console.log(chalk.blue('👀 Generating HTML preview...'));
      
      if (!await fs.pathExists(input)) {
        console.error(chalk.red(`❌ Input file not found: ${input}`));
        process.exit(1);
      }
      
      const converter = new MD2PDFConverter();
      
      if (options.template && options.template !== 'default') {
        try {
          converter.usePresetTemplate(options.template);
        } catch (error) {
          console.warn(chalk.yellow(`⚠️ Unknown template: ${options.template}`));
        }
      }
      
      if (options.css && await fs.pathExists(options.css)) {
        const customCSS = await fs.readFile(options.css, 'utf8');
        converter.setCustomCSS(customCSS);
      }
      
      const result = await converter.previewHTML(input);
      
      const outputPath = options.output || input.replace(/\.(md|markdown)$/i, '.html');
      await fs.writeFile(outputPath, result.html, 'utf8');
      
      console.log(chalk.green('✅ HTML preview generated!'));
      console.log(chalk.cyan(`📄 Output: ${outputPath}`));
      
      if (result.metadata.title) {
        console.log(chalk.cyan(`📝 Title: ${result.metadata.title}`));
      }
      
    } catch (error) {
      console.error(chalk.red('❌ Preview generation failed:'), error.message);
      process.exit(1);
    }
  });

// 信息命令
program
  .command('info <input>')
  .alias('i')
  .description('Show information about a Markdown file')
  .action(async (input) => {
    try {
      if (!await fs.pathExists(input)) {
        console.error(chalk.red(`❌ Input file not found: ${input}`));
        process.exit(1);
      }
      
      const converter = new MD2PDFConverter();
      const info = await converter.getDocumentInfo(input);
      
      console.log(chalk.blue('📊 Document Information:'));
      console.log(chalk.cyan(`📁 File: ${info.filePath}`));
      console.log(chalk.cyan(`📏 Size: ${(info.fileSize / 1024).toFixed(2)} KB`));
      console.log(chalk.cyan(`📝 Words: ${info.wordCount}`));
      console.log(chalk.cyan(`🔤 Characters: ${info.characterCount}`));
      console.log(chalk.cyan(`📅 Modified: ${info.lastModified.toLocaleString()}`));
      
      if (info.title) {
        console.log(chalk.cyan(`📋 Title: ${info.title}`));
      }
      
      if (info.headings.length > 0) {
        console.log(chalk.cyan(`📑 Headings: ${info.headings.length}`));
      }
      
      if (info.images.length > 0) {
        console.log(chalk.cyan(`🖼️ Images: ${info.images.length}`));
      }
      
      if (info.tables > 0) {
        console.log(chalk.cyan(`📊 Tables: ${info.tables}`));
      }
      
      if (info.codeBlocks > 0) {
        console.log(chalk.cyan(`💻 Code blocks: ${info.codeBlocks}`));
      }
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to get document info:'), error.message);
      process.exit(1);
    }
  });

// 解析选项
function parseOptions(options) {
  const result = {
    format: options.format || 'A4',
    printBackground: !options.noBackground,
    timeout: parseInt(options.timeout) || 30000
  };
  
  // 解析边距
  if (options.margin) {
    if (options.margin.includes(':')) {
      // 格式: "top:2cm,bottom:1cm,left:1cm,right:1cm"
      const margins = {};
      options.margin.split(',').forEach(part => {
        const [key, value] = part.split(':');
        margins[key.trim()] = value.trim();
      });
      result.margin = margins;
    } else {
      // 格式: "1cm" (所有边距相同)
      result.margin = {
        top: options.margin,
        bottom: options.margin,
        left: options.margin,
        right: options.margin
      };
    }
  }
  
  // 页眉页脚
  if (options.header) {
    result.displayHeaderFooter = true;
    result.headerTemplate = options.header;
  }
  
  if (options.footer) {
    result.displayHeaderFooter = true;
    result.footerTemplate = options.footer;
  }
  
  return result;
}

program.parse();
